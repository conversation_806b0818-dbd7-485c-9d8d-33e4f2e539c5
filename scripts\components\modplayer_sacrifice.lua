-- 玩家献祭系统组件
local GLOBAL = rawget(_G, "GLOBAL") or _G
local TUNING = GLOBAL.TUNING or {}

local ModPlayerSacrifice = Class(function(self, inst)
    self.inst = inst

    -- 统计
    self.stats = { blood=0, soul=0, hunger=0, item=0, wins=0, fails=0 }
    -- 永久上限改动（可正可负）
    self.permanent = { health = 0, sanity = 0, hunger = 0 }
    -- 诅咒列表：{ {id="curse_id", expire_day=第N天}, ... }
    self.curses = {}

    -- 限制
    self.last_spin_time = 0
    self.last_spin_day = -1
    self.daily_used = 0

    -- 历史记录（最多10条）
    self.history = {}

    -- 定时清理过期诅咒
    inst:DoPeriodicTask(10, function() self:TickCurses() end)

    -- 捕获初始基础上限（延迟一帧，确保组件就绪）
    self.base_max = { health = 100, sanity = 200, hunger = 150 }
    inst:DoTaskInTime(0, function()
        if inst and inst.components then
            if inst.components.health then self.base_max.health = inst.components.health.maxhealth or self.base_max.health end
            if inst.components.sanity then self.base_max.sanity = inst.components.sanity.max or self.base_max.sanity end
            if inst.components.hunger then self.base_max.hunger = inst.components.hunger.max or self.base_max.hunger end
        end
    end)
end)

-- 应用永久上限改动（登录/加载后调用）
function ModPlayerSacrifice:ApplyPermanentCaps()
    local p = self.inst
    if not p or not p:IsValid() then return end
    local lb = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.SACRIFICE and TUNING.CARAVAN.SACRIFICE.PENALTY and TUNING.CARAVAN.SACRIFICE.PENALTY.LOWER_BOUNDS) or {health=50,sanity=50,hunger=100}

    local base_health = (self.base_max and self.base_max.health) or 100
    local base_sanity = (self.base_max and self.base_max.sanity) or 200
    local base_hunger = (self.base_max and self.base_max.hunger) or 150

    -- Health
    if p.components.health then
        local new = math.max(lb.health or 50, base_health + (self.permanent.health or 0))
        local percent = p.components.health:GetPercent()
        p.components.health:SetMaxHealth(new)
        p.components.health:SetPercent(percent)
    end

    -- Sanity
    if p.components.sanity then
        local new = math.max(lb.sanity or 50, base_sanity + (self.permanent.sanity or 0))
        local percent = p.components.sanity:GetPercent()
        p.components.sanity:SetMax(new)
        p.components.sanity:SetPercent(percent)
    end

    -- Hunger
    if p.components.hunger then
        local new = math.max(lb.hunger or 100, base_hunger + (self.permanent.hunger or 0))
        local percent = p.components.hunger:GetPercent()
        p.components.hunger:SetMax(new)
        p.components.hunger:SetPercent(percent)
    end
end

function ModPlayerSacrifice:CanSpin()
    local sac = TUNING.CARAVAN.SACRIFICE
    local now = GLOBAL.GetTime()
    local day = (GLOBAL.TheWorld and GLOBAL.TheWorld.state and GLOBAL.TheWorld.state.cycles) or 0

    if self.last_spin_day ~= day then
        self.daily_used = 0
        self.last_spin_day = day
    end

    if self.daily_used >= (sac.LIMITS.DAILY_SPINS_LIMIT or 3) then
        return false, "今日次数用尽"
    end

    if now - (self.last_spin_time or 0) < (sac.LIMITS.COOLDOWN_SECONDS or 30) then
        return false, "冷却中"
    end

    if self.inst:HasTag("playerghost") then
        return false, "幽灵状态不可献祭"
    end

    return true
end

function ModPlayerSacrifice:MarkSpinUsed()
    local day = (GLOBAL.TheWorld and GLOBAL.TheWorld.state and GLOBAL.TheWorld.state.cycles) or 0
    self.last_spin_time = GLOBAL.GetTime()
    if self.last_spin_day ~= day then
        self.daily_used = 1
        self.last_spin_day = day
    else
        self.daily_used = (self.daily_used or 0) + 1
    end
end

function ModPlayerSacrifice:AddPermanentDelta(kind, delta)
    delta = delta or 0
    self.permanent[kind] = (self.permanent[kind] or 0) + delta

    -- 下限保护：确保不会低于配置的最低值
    local lb = (GLOBAL.TUNING and GLOBAL.TUNING.CARAVAN and GLOBAL.TUNING.CARAVAN.SACRIFICE and GLOBAL.TUNING.CARAVAN.SACRIFICE.PENALTY and GLOBAL.TUNING.CARAVAN.SACRIFICE.PENALTY.LOWER_BOUNDS) or {health=50,sanity=50,hunger=100}
    local base_max = (self.base_max and self.base_max[kind]) or self:GetBaseMaxValue(kind)
    local target_max = base_max + self.permanent[kind]
    local min_allowed = lb[kind] or 50

    if target_max < min_allowed then
        -- 调整permanent值以不低于下限
        self.permanent[kind] = min_allowed - base_max
    end

    self:ApplyPermanentCaps()
end

function ModPlayerSacrifice:GetBaseMaxValue(kind)
    -- 获取基础最大值（不含permanent修正）
    if kind == "health" then return 100 end
    if kind == "sanity" then return 200 end
    if kind == "hunger" then return 150 end
    return 100
end
function ModPlayerSacrifice:AddLuckBoost(value, duration)
    self.luck_boost = {
        value = value or 0.2,
        expire_time = GLOBAL.GetTime() + (duration or 0)
    }
end

function ModPlayerSacrifice:GetLuckBoost()
    if not self.luck_boost then return 0 end
    if GLOBAL.GetTime() > (self.luck_boost.expire_time or 0) then
        self.luck_boost = nil
        return 0
    end
    return self.luck_boost.value or 0
end

function ModPlayerSacrifice:SetTitle(title)
    self.title = title or ""
end

function ModPlayerSacrifice:GetTitle()
    return self.title or ""
end

function ModPlayerSacrifice:AddCurse(id, days)
    local day = (GLOBAL.TheWorld and GLOBAL.TheWorld.state and GLOBAL.TheWorld.state.cycles) or 0
    table.insert(self.curses, {id=id, expire_day=day + (days or 1)})
end

function ModPlayerSacrifice:TickCurses()
    local day = (GLOBAL.TheWorld and GLOBAL.TheWorld.state and GLOBAL.TheWorld.state.cycles) or 0
    local kept = {}
    for _, c in ipairs(self.curses or {}) do
        if (c.expire_day or day) > day then table.insert(kept, c) end
    end
    self.curses = kept
end

function ModPlayerSacrifice:GetSnapshot()
    local pending = nil
    if self._pending and self._pending.time and self._pending.time > GLOBAL.GetTime() then
        pending = { t = self._pending.t, token = self._pending.token, time = self._pending.time }
    end
    return GLOBAL.json and GLOBAL.json.encode({
        stats = self.stats,
        permanent = self.permanent,
        curses = self.curses,
        history = self.history,
        last_spin_time = self.last_spin_time,
        last_spin_day = self.last_spin_day,
        daily_used = self.daily_used,
        pending = pending,
    }) or "{}"
end

function ModPlayerSacrifice:_PushHistory(entry)
    table.insert(self.history, 1, entry)
    while #self.history > 10 do table.remove(self.history) end
end

function ModPlayerSacrifice:OnSave()
    return {
        stats = self.stats,
        permanent = self.permanent,
        curses = self.curses,
        history = self.history,
        last_spin_time = self.last_spin_time,
        last_spin_day = self.last_spin_day,
        daily_used = self.daily_used,
    }
end

function ModPlayerSacrifice:OnLoad(data)
    if not data then return end
    self.stats = data.stats or self.stats
    self.permanent = data.permanent or self.permanent
    self.curses = data.curses or self.curses
    self.last_spin_time = data.last_spin_time or 0
    self.last_spin_day = data.last_spin_day or -1
    self.daily_used = data.daily_used or 0
    self.history = data.history or {}

    -- 延迟应用上限
    self.inst:DoTaskInTime(1, function()
        self:ApplyPermanentCaps()
    end)
end

return ModPlayerSacrifice

