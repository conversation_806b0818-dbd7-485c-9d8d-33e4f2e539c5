local _G = GLOBAL
GLOBAL.setmetatable(env, { __index = function(_, k) return GLOBAL.rawget(GLOBAL, k) end })

-- Basic helpers
local DEBUG_MODE = GetModConfigData("debug_mode") or false

local function Announce(msg)
    if TheNet:GetIsServer() then
        TheNet:Announce("[商旅巡游录] " .. tostring(msg))
    end
end

local function ModSay(player, msg)
    if player and player.components and player.components.talker then
        player.components.talker:Say("[商旅巡游录] " .. tostring(msg))
    end
end

local function DebugPrint(...)
    if DEBUG_MODE then
        print("[商旅巡游录-调试]", ...)
    end
end

-- 导出调试函数
rawset(_G, "CARAVAN_DEBUG_PRINT", DebugPrint)

-- Load tuning first, then components
modimport("scripts/tuning.lua")

-- Prefabs
PrefabFiles = PrefabFiles or {}
local add = table.insert
add(PrefabFiles, "caravan_light")


-- Register components files (load them so they're available)
modimport("scripts/components/" .. "modworld_global" .. ".lua")
modimport("scripts/components/" .. "modplayer_boons" .. ".lua")
modimport("scripts/components/" .. "modplayer_rep" .. ".lua")
modimport("scripts/components/" .. "modplayer_reroll" .. ".lua")
modimport("scripts/components/" .. "modplayer_sacrifice" .. ".lua")
modimport("scripts/systems/" .. "sacrifice_wheel" .. ".lua")
modimport("scripts/systems/" .. "rarity" .. ".lua")

-- Components paths
local WORLD_COMP = "modworld_global"
local PLAYER_BOONS = "modplayer_boons"
local PLAYER_REP = "modplayer_rep"
local PLAYER_SAC = "modplayer_sacrifice"

-- Attach world component early so its OnLoad runs properly
AddPrefabPostInit("world", function(inst)
    if inst.ismastersim then
        if not inst.components[WORLD_COMP] then
            inst:AddComponent(WORLD_COMP)
        end
    end
end)

-- Attach player components
AddPlayerPostInit(function(inst)
    if not TheWorld or not TheWorld.ismastersim then return end
    if not inst.components[PLAYER_BOONS] then
        inst:AddComponent(PLAYER_BOONS)
    end
    if not inst.components[PLAYER_REP] then
        inst:AddComponent(PLAYER_REP)
    end
    if not inst.components[PLAYER_SAC] then
        inst:AddComponent(PLAYER_SAC)
    end
    if not inst.components["modplayer_reroll"] then
        inst:AddComponent("modplayer_reroll")
    end

    -- 延迟应用词条效果，确保所有组件都已初始化
    inst:DoTaskInTime(1, function()
        if inst:IsValid() and not inst:HasTag("playerghost") then
            local MutatorEffects = require("systems/mutator_effects")
            if MutatorEffects then
                MutatorEffects.ApplyToPlayer(inst)
                print("[商旅巡游录] 已为玩家应用词条效果:", inst.userid or "unknown")
            end
        end
    end)

    -- 监听玩家重生事件，重新应用效果
    inst:ListenForEvent("ms_respawnedfromghost", function()
        inst:DoTaskInTime(0.5, function()
            if inst:IsValid() and not inst:HasTag("playerghost") then
                local MutatorEffects = require("systems/mutator_effects")
                if MutatorEffects then
                    MutatorEffects.ApplyToPlayer(inst)
                    print("[商旅巡游录] 玩家重生后重新应用词条效果:", inst.userid or "unknown")
                end

                -- 重新应用被动恩惠效果
                if inst.components.modplayer_boons then
                    inst.components.modplayer_boons:ReapplyAllEffects()
                    print("[商旅巡游录] 玩家重生后重新应用被动恩惠效果:", inst.userid or "unknown")
                end
            end
        end)
    end)

    -- 监听玩家变成幽灵事件，清除词条效果
    inst:ListenForEvent("ms_becameghost", function()
        local MutatorEffects = require("systems/mutator_effects")
        if MutatorEffects then
            MutatorEffects.RemoveFromPlayer(inst)
            print("[商旅巡游录] 玩家变成幽灵，清除词条效果:", inst.userid or "unknown")
        end
    end)

    -- 监听玩家加入事件，确保新加入的玩家也能获得词条效果
    inst:ListenForEvent("playeractivated", function()
        inst:DoTaskInTime(2, function()
            if inst:IsValid() and not inst:HasTag("playerghost") then
                local MutatorEffects = require("systems/mutator_effects")
                if MutatorEffects then
                    MutatorEffects.ApplyToPlayer(inst)
                    print("[商旅巡游录] 玩家激活后应用词条效果:", inst.userid or "unknown")
                end
            end
        end)
    end)
end)

-- Load commands after game initialization
AddSimPostInit(function()
    modimport("scripts/systems/commands.lua")
    if TheWorld and TheWorld.ismastersim then
        Announce("已加载：每日词条、合约、恩惠、献祭、商队、Boss进化（默认开启）。输入 chelp 查看指令或按 V 键打开UI")
    end
end)

-- UI System
local CaravanScreen = require("screens/caravanscreen")
local SacrificeWheel = require("systems/sacrifice_wheel")
local Rarity = require("systems/rarity")

-- Mutator effects system will be required where needed:
-- require("systems/mutator_effects")

-- Global variable to track UI state
local caravan_ui_open = false

-- Simple key handler for V key
local function ToggleCaravanUI()
    local player = ThePlayer
    if not player then return end

    if caravan_ui_open then
        -- Close UI
        TheFrontEnd:PopScreen()
        caravan_ui_open = false
    else
        -- Open UI
        local screen = CaravanScreen(player)
        TheFrontEnd:PushScreen(screen)
        caravan_ui_open = true

        -- Listen for screen close to update state
        screen.inst:ListenForEvent("onremove", function()
            caravan_ui_open = false
        end)
    end
end

-- Expose function globally for chat commands
rawset(_G, "ToggleCaravanUI", ToggleCaravanUI)

-- Register V key handler
AddSimPostInit(function()
    if TheInput then
        TheInput:AddKeyDownHandler(KEY_V, ToggleCaravanUI)
    end
end)

-- Expose helpers to systems
rawset(_G, "CARAVAN_MOD_ANNOUNCE", Announce)
rawset(_G, "CARAVAN_MOD_SAY", ModSay)

-- 注册RPC处理器（服务端）
-- === 献祭：快照/执行 ===
AddModRPCHandler("thinking", "sacrifice_request", function(player)
    if not player or not TheWorld or not TheWorld.ismastersim then return end
    local comp = player.components and player.components.modplayer_sacrifice
    if not comp then return end
    local payload = comp:GetSnapshot()
    SendModRPCToClient(GetClientModRPC("thinking", "sacrifice_receive"), player.userid, payload)
end)

AddModRPCHandler("thinking", "sacrifice_action", function(player, action, p1, p2)
    if not player or not TheWorld or not TheWorld.ismastersim then return end
    local comp = player.components and player.components.modplayer_sacrifice
    if not comp then return end

    action = tostring(action or "")
    local ok, msg = false, nil
    if action == "spin" and p1 then
        local t = tostring(p1)
        local opt = {}
        if t == "item" then
            -- 取手持物
            opt.item = player.components.inventory and player.components.inventory:GetActiveItem() or nil
        end
        ok, msg = SacrificeWheel.DoSpin(player, t, opt)
    elseif action == "confirm" and p1 and p2 then
        local t = tostring(p1)
        local opt = { confirm_token = tostring(p2) }
        if t == "item" then
            opt.item = player.components.inventory and player.components.inventory:GetActiveItem() or nil
        end
        -- 令牌幂等：确认路径也会在服务端再次校验类型/令牌/时间窗
        ok, msg = SacrificeWheel.DoSpin(player, t, opt)
    end

    -- 回传最新快照（以及简单结果）
    local payload = comp:GetSnapshot()
    SendModRPCToClient(GetClientModRPC("thinking", "sacrifice_receive"), player.userid, payload)
    if player.components.talker and msg then
        player.components.talker:Say("献祭结果:"..tostring(msg))
    end
end)
-- 稀有度扫描RPC
AddModRPCHandler("thinking", "rarity_scan", function(player)
    if not player or not player.components or not player.components.inventory then return end

    local talker = player.components.talker
    local function say(msg) if talker then talker:Say(msg) end end

    local inv = player.components.inventory
    local items = {}

    -- 扫描背包
    for i = 1, inv:GetNumSlots() do
        local item = inv:GetItemInSlot(i)
        if item then
            local grade = Rarity.GetItemRarity(item)
            table.insert(items, {name = item.prefab, grade = grade})
        end
    end

    -- 扫描装备
    for _, slot in ipairs({"hands", "head", "body"}) do
        local item = inv:GetEquippedItem(GLOBAL.EQUIPSLOTS[string.upper(slot)])
        if item then
            local grade = Rarity.GetItemRarity(item)
            table.insert(items, {name = item.prefab, grade = grade, equipped = true})
        end
    end

    if #items == 0 then
        say("背包和装备栏为空")
        return
    end

    say(string.format("扫描到 %d 个物品:", #items))
    for i, item in ipairs(items) do
        local eq = item.equipped and " [装备]" or ""
        say(string.format("%d. %s (%s级)%s", i, item.name, item.grade, eq))
        if i >= 10 then
            say("...（仅显示前10个）")
            break
        end
    end
end)

-- 稀有度调试RPC
AddModRPCHandler("thinking", "rarity_debug", function(player)
    if not player or not player.components or not player.components.inventory then return end

    local talker = player.components.talker
    local function say(msg) if talker then talker:Say(msg) end end

    local item = player.components.inventory:GetActiveItem()
    if not item then
        say("请手持一个物品")
        return
    end

    local explanation = Rarity.Explain(item)
    say(string.format("物品: %s", item.prefab))
    say(string.format("稀有度: %s", explanation.grade))
    say(string.format("基础分: %d", explanation.base_score))

    if explanation.tag_bonuses and #explanation.tag_bonuses > 0 then
        say("标签加成:")
        for _, bonus in ipairs(explanation.tag_bonuses) do
            say(string.format("  %s: +%d", bonus.tag, bonus.score))
        end
    end

    if explanation.component_bonuses and #explanation.component_bonuses > 0 then
        say("组件加成:")
        for _, bonus in ipairs(explanation.component_bonuses) do
            say(string.format("  %s: +%d", bonus.component, bonus.score))
        end
    end

    say(string.format("总分: %d", explanation.total_score))
end)

AddModRPCHandler("thinking", "contract_request", function(player)
    if not player or not TheWorld or not TheWorld.ismastersim then return end

    local world_comp = TheWorld.components.modworld_global
    if world_comp then
        local contract_data = world_comp:GetContractsSnapshotForClient(player)
        SendModRPCToClient(GetClientModRPC("thinking", "contract_receive"), player.userid, contract_data)
    end
end)

AddModRPCHandler("thinking", "contract_deliver", function(player, contract_index)
    if not player or not TheWorld or not TheWorld.ismastersim then return end

    local world_comp = TheWorld.components.modworld_global
    if world_comp then
        world_comp:CmdDeliverContract(player, tostring(contract_index))

        -- 通知客户端刷新
        local updated_data = world_comp:GetContractsSnapshotForClient(player)
        SendModRPCToClient(GetClientModRPC("thinking", "contract_receive"), player.userid, updated_data)
    end
end)

-- 词条重掷（服务端）
AddModRPCHandler("thinking", "mutators_reroll", function(player)
    if not player or not TheWorld or not TheWorld.ismastersim then return end

    local world_comp = TheWorld.components.modworld_global
    if world_comp and world_comp.CmdRerollMutators then
        world_comp:CmdRerollMutators(player)

        -- 可选：通知客户端刷新词条页
        local data = world_comp:GetContractsSnapshotForClient(player)
        SendModRPCToClient(GetClientModRPC("thinking", "contract_receive"), player.userid, data)
    end
end)

-- 注册客户端RPC处理器
AddClientModRPCHandler("thinking", "sacrifice_receive", function(payload)
    if not payload then return end
    local ok, data = pcall(json.decode, payload)
    if ok and data then
        if TheFrontEnd and TheFrontEnd.GetActiveScreen then
            local screen = TheFrontEnd:GetActiveScreen()
            if screen and screen.name == "CaravanScreen" and screen.OnSacrificeDataUpdated then
                screen:OnSacrificeDataUpdated(data)
            end
        end
    end
end)
-- 注册客户端RPC处理器
AddClientModRPCHandler("thinking", "contract_receive", function(contract_data)
    if not contract_data then return end

    local success, data = pcall(json.decode, contract_data)
    if success and data and data.contracts then
        -- 通知当前打开的UI
        if TheFrontEnd and TheFrontEnd.GetActiveScreen then
            local screen = TheFrontEnd:GetActiveScreen()
            if screen and screen.name == "CaravanScreen" then
                screen:OnContractDataUpdated(data.contracts)
            end
        end
    end
end)

-- 请求/接收今日词条
AddModRPCHandler("thinking", "mutators_request", function(player)
    if not player or not TheWorld or not TheWorld.ismastersim then return end
    local world_comp = TheWorld.components.modworld_global
    if world_comp and world_comp.GetMutatorsSnapshotForClient then
        local payload = world_comp:GetMutatorsSnapshotForClient(player)
        SendModRPCToClient(GetClientModRPC("thinking", "mutators_receive"), player.userid, payload)
    end
end)

AddClientModRPCHandler("thinking", "mutators_receive", function(payload)
    if not payload then return end
    local ok, data = pcall(json.decode, payload)
    if ok and data and data.mutators then
        if TheFrontEnd and TheFrontEnd.GetActiveScreen then
            local screen = TheFrontEnd:GetActiveScreen()
            if screen and screen.name == "CaravanScreen" and screen.OnMutatorsDataUpdated then
                screen:OnMutatorsDataUpdated(data.mutators)
            end
        end
    end
end)

-- 请求/接收商队信息
AddModRPCHandler("thinking", "caravan_request", function(player)
    if not player or not TheWorld or not TheWorld.ismastersim then return end
    local world_comp = TheWorld.components.modworld_global
    if world_comp and world_comp.GetCaravanSnapshotForClient then
        local payload = world_comp:GetCaravanSnapshotForClient(player)
        SendModRPCToClient(GetClientModRPC("thinking", "caravan_receive"), player.userid, payload)
    end
end)

AddClientModRPCHandler("thinking", "caravan_receive", function(payload)
    if not payload then return end
    local ok, data = pcall(json.decode, payload)
    if ok and data and data.caravan then
        if TheFrontEnd and TheFrontEnd.GetActiveScreen then
            local screen = TheFrontEnd:GetActiveScreen()
            if screen and screen.name == "CaravanScreen" and screen.OnCaravanDataUpdated then
                screen:OnCaravanDataUpdated(data.caravan)
            end
        end
    end
end)

-- 内测：客户端允许刷新合约（不限制）
AddModRPCHandler("thinking", "contracts_refresh", function(player)
    if not player or not TheWorld or not TheWorld.ismastersim then return end
    local world_comp = TheWorld.components.modworld_global
    if world_comp and world_comp.GenerateContracts then
        world_comp:GenerateContracts()
        if world_comp.SyncContractProgress then
            world_comp:SyncContractProgress()
        end
        local snapshot = world_comp:GetContractsSnapshotForClient(player)
        SendModRPCToClient(GetClientModRPC("thinking", "contract_receive"), player.userid, snapshot)
    end
end)

-- 内测：刷新单个合约（不限）
AddModRPCHandler("thinking", "contract_refresh_one", function(player, index)
    if not player or not TheWorld or not TheWorld.ismastersim then return end
    local world_comp = TheWorld.components.modworld_global
    if world_comp and world_comp.RefreshSingleContract then
        local ok = world_comp:RefreshSingleContract(index)
        if ok then
            local snapshot = world_comp:GetContractsSnapshotForClient(player)
            SendModRPCToClient(GetClientModRPC("thinking", "contract_receive"), player.userid, snapshot)
        end
    end
end)

-- Components already loaded above


-- 被动恩惠：请求/接收/动作（RPC）
AddModRPCHandler("thinking", "boons_request", function(player)
    if not player or not TheWorld or not TheWorld.ismastersim then return end
    local boons = player.components and player.components.modplayer_boons
    if not boons then return end
    local snapshot = {
        favor = boons.favor or 0,
        unlocked_boons = boons.unlocked_boons or {},
        equipped_boons = boons.equipped_boons or {},
        max_equipped = boons.max_equipped or 2,
    }
    local payload = json.encode(snapshot)
    SendModRPCToClient(GetClientModRPC("thinking", "boons_receive"), player.userid, payload)
end)

AddModRPCHandler("thinking", "boons_action", function(player, action, p1, p2)
    if not player or not TheWorld or not TheWorld.ismastersim then return end
    local boons = player.components and player.components.modplayer_boons
    if not boons then return end

    action = tostring(action or "")

    if action == "unlock" and p1 then
        boons:UnlockBoon(tostring(p1), true)
    elseif action == "equip" and p1 then
        boons:EquipBoon(tostring(p1), true)
    elseif action == "unequip" and p1 then
        boons:UnequipBoon(tostring(p1), true)
    elseif action == "respec" then
        local cost = (#(boons.equipped_boons or {})) * 10
        boons:RespecAllBoons(cost)
    elseif action == "teleport" and p1 and p2 then
        -- 客户端请求瞬移到鼠标位置
        local x = tonumber(p1)
        local z = tonumber(p2)
        if x and z then
            -- 校验是否拥有被动与冷却
            local has = false
            if boons.IsBoonEquipped then
                has = boons:IsBoonEquipped("mage_teleport")
            else
                local eq = boons.equipped_boons or {}
                for _, id in ipairs(eq) do if id == "mage_teleport" then has = true break end end
            end
            if has then
                local def = (boons.GetBoonDef and boons:GetBoonDef("mage_teleport")) or { effect_value = { distance = 8, cooldown = 30 } }
                local dist = (def.effect_value and def.effect_value.distance) or 8
                local cd = (def.effect_value and def.effect_value.cooldown) or 30
                local now = GLOBAL.GetTime()
                if not boons.teleport_cooldown or now >= boons.teleport_cooldown then
                    -- 夹紧到最大距离
                    local px, _, pz = player.Transform:GetWorldPosition()
                    local dx, dz = x - px, z - pz
                    local d = math.sqrt(dx*dx + dz*dz)
                    if d > dist then
                        local r = dist / d
                        x = px + dx * r
                        z = pz + dz * r
                    end
                    -- 地形校验
                    local tile = TheWorld.Map:GetTileAtPoint(x, 0, z)
                    if tile ~= GROUND.IMPASSABLE and tile ~= GROUND.INVALID then
                        if player.Physics then player.Physics:Teleport(x,0,z) else player.Transform:SetPosition(x,0,z) end
                        boons.teleport_cooldown = now + cd
                        if player.SoundEmitter then player.SoundEmitter:PlaySound("dontstarve/common/teleportworm/teleport") end
                    end
                else
                    if player.components.talker then
                        player.components.talker:Say(string.format("闪现冷却中(%.0fs)", boons.teleport_cooldown - now))
                    end
                end
            end
        end
    end

    -- 向客户端回传最新快照
    local snapshot = {
        favor = boons.favor or 0,
        unlocked_boons = boons.unlocked_boons or {},
        equipped_boons = boons.equipped_boons or {},
        max_equipped = boons.max_equipped or 2,
    }
    local payload = json.encode(snapshot)
    SendModRPCToClient(GetClientModRPC("thinking", "boons_receive"), player.userid, payload)
end)


-- 客户端：闪现术输入监听（仅当装备“闪现术”时有效）
if TheNet:IsDedicated() == false then
    local input = TheInput
    if input then
        -- 模块级状态
        CARAVAN_TP = {
            active = false,
            window = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.TELEPORT_DOUBLE_TAP_WINDOW) or 0.25,
            enable_double = TUNING and TUNING.CARAVAN and TUNING.CARAVAN.TELEPORT_DOUBLE_TAP or false,
            enable_hotkey = TUNING and TUNING.CARAVAN and TUNING.CARAVAN.TELEPORT_ENABLE_HOTKEY or false,
            hotkey_name = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.TELEPORT_HOTKEY) or "R",
            last_tap = { up=0, down=0, left=0, right=0, w=0, s=0, a=0, d=0 },
        }

        local keymap = { R=KEY_R, F=KEY_F, Q=KEY_Q, E=KEY_E, SPACE=KEY_SPACE }

        local function try_teleport()
            if not CARAVAN_TP.active then return end
            local pos = input:GetWorldPosition()
            if pos then
                SendModRPCToServer(GetModRPC("thinking","boons_action"), "teleport", tostring(pos.x), tostring(pos.z))
            end
        end

        input:AddKeyDownHandler(function(key)
            if not CARAVAN_TP.active then return end
            if input:IsKeyDown(KEY_CTRL) or input:IsKeyDown(KEY_ALT) then return end
            if TheFrontEnd and (TheFrontEnd.consoletext and TheFrontEnd.consoletext.shown or TheFrontEnd:GetOpenScreenOfType("ChatInputScreen")) then return end

            -- 双击方向键/WSAD
            if CARAVAN_TP.enable_double then
                local map = {
                    [KEY_UP]="up", [KEY_DOWN]="down", [KEY_LEFT]="left", [KEY_RIGHT]="right",
                    [KEY_W]="w", [KEY_S]="s", [KEY_A]="a", [KEY_D]="d",
                }
                local name = map[key]
                if name then
                    local now = GetTime()
                    if now - (CARAVAN_TP.last_tap[name] or 0) <= (CARAVAN_TP.window or 0.25) then
                        try_teleport()
                    end
                    CARAVAN_TP.last_tap[name] = now
                end
            end

            -- 自定义快捷键
            if CARAVAN_TP.enable_hotkey and CARAVAN_TP.hotkey_name then
                local hk = keymap[string.upper(CARAVAN_TP.hotkey_name) or "R"]
                if hk and key == hk then
                    try_teleport()
                end
            end
        end)
    end
end

AddClientModRPCHandler("thinking", "boons_receive", function(payload)

    if not payload then return end
    local ok, data = pcall(json.decode, payload)
    if not ok or not data then return end

    -- 根据快照是否装备“闪现术”启用/禁用输入监听
    if CARAVAN_TP then
        local equipped = data.equipped_boons or {}
        local has_teleport = false
        for _, id in ipairs(equipped) do if id == "mage_teleport" then has_teleport = true break end end
        CARAVAN_TP.active = has_teleport
        -- 同步配置（支持运行时调整）
        CARAVAN_TP.window = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.TELEPORT_DOUBLE_TAP_WINDOW) or CARAVAN_TP.window
        CARAVAN_TP.enable_double = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.TELEPORT_DOUBLE_TAP) or CARAVAN_TP.enable_double
        CARAVAN_TP.enable_hotkey = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.TELEPORT_ENABLE_HOTKEY) or CARAVAN_TP.enable_hotkey
        CARAVAN_TP.hotkey_name = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.TELEPORT_HOTKEY) or CARAVAN_TP.hotkey_name
    end

    local player = ThePlayer
    if not player or not player.components or not player.components.modplayer_boons then return end

    if player.components.modplayer_boons.ApplySnapshot then
        player.components.modplayer_boons:ApplySnapshot(data)
    else
        -- 直接赋值作为后备
        player.components.modplayer_boons.favor = data.favor or 0
        player.components.modplayer_boons.unlocked_boons = data.unlocked_boons or {}
        player.components.modplayer_boons.equipped_boons = data.equipped_boons or {}
        player.components.modplayer_boons.max_equipped = data.max_equipped or 2
    end

    -- 若UI已打开，刷新被动恩惠页
    if TheFrontEnd and TheFrontEnd.GetActiveScreen then
        local screen = TheFrontEnd:GetActiveScreen()
        if screen and screen.name == "CaravanScreen" and screen.UpdateBoonsContent then
            screen:UpdateBoonsContent()
        end
    end
end)


-- Components already loaded above
