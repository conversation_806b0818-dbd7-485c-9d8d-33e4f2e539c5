## 原理

饥荒单机里的猪镇，里面玩家的家就是个小房子，进房子里不是重新加载了一个场景，而是小房子本身就在地图的某个角落，进房子跟跳虫洞是一样的，直接把玩家传送到那个在地图之外的一片区域去了

- 小房子内的地皮是一张贴图，貌似官方的也是，没法用草叉挖起来
- 四周的墙壁都是贴图，如果锁视角，会简单些，不锁视角的话，可能还要处理四周的贴图变换
- 要设置屋内的世界状态（比如光照，是否会漏雨，是否会被雷劈，自燃等等，其实就是创建世界的那些设置）
- 要设置边界不可通行
- ...

要想做好，需要对主世界的很多状态，设定等进行hook处理，总之就是很麻烦

*神话书说 广寒宫 里，在外面世界下雨时，嫦娥也会潮湿，应该就是没有对广寒宫那片区域能否降雨做限制*

## 案例

**北甍**大佬写的 [糖果屋](https://steamcommunity.com/sharedfiles/filedetails/?id=2623494521) 就是个功能单一的小房子mod，有兴趣的可以去点个赞看看源码，注释写的也很全