---@meta

---@class component_teleporter
---@field inst ent #挂载该组件的实体
---@field targetTeleporter ent|nil #目标传送门实体
---@field onActivate fun(inst:ent,doer:ent,migration_data:table)|nil #激活传送门时的回调
---@field onActivateByOther fun(inst:ent,source:ent,doer:ent,item:ent)|nil #被其他对象激活时的回调,这里可以是人物也可以是物品，即只需要在doer和item之间选一个参数填上
---@field offset number #传送落点的偏移距离
---@field enabled boolean #传送门是否启用
---@field numteleporting integer #当前正在传送的对象数量
---@field teleportees table<ent,true> #正在准备传送的对象表
---@field saveenabled boolean #是否保存targetTeleporter
---@field travelcameratime number #传送时相机淡入淡出时间
---@field travelarrivetime number #传送到达动画时间
---@field items table<ent,true> #正在传送的物品表
---@field _onremoveteleportee fun(doer:ent) #传送对象移除时的回调
---@field targetTeleporterTemporary ent|nil #临时目标传送门（一次性）
---@field stopcamerafades boolean|nil #是否跳过相机淡入淡出
---@field migration_data table|nil #跨世界传送