-- Tuning file needs GLO<PERSON>L access

GLOBAL.TUNING.CARAVAN = {
    DIFFICULTY = GetModConfigData and GetModConfigData("difficulty") or "normal",
    MUTATOR_COUNT = GetModConfigData and GetModConfigData("mutator_count") or 2,
    CONTRACT_COUNT = GetModConfigData and GetModConfigData("contract_count") or 3,
    CONTRACTS_CONCURRENT = GetModConfigData and GetModConfigData("contracts_concurrent") or 4,
    FAVOR_MULTIPLIER = GetModConfigData and GetModConfigData("favor_multiplier") or 1.0,
    CARAVAN_PERIOD_DAYS = GetModConfigData and GetModConfigData("caravan_period") or 7,
    ENABLE_BOSS_EVO = GetModConfigData and GetModConfigData("enable_boss_evo") ~= false,

    -- 交付限制配置
    DELIVER_MIN_DURABILITY = GetModConfigData and GetModConfigData("deliver_min_durability") or 0.8,
    DELIVER_MIN_FRESHNESS = GetModConfigData and GetModConfigData("deliver_min_freshness") or 0.5,

    -- 目标池配置
    TARGET_REACHABILITY = GetModConfigData and GetModConfigData("target_reachability") or 0.3,
    MOD_CONTENT_WEIGHT = GetModConfigData and GetModConfigData("mod_content_weight") or 0.8,

    -- UI相关常量
    MAX_REPUTATION = 100, -- 最大声望值
    MAX_CARAVAN_WAIT_DAYS = 20, -- 最大商队等待天数

    -- 被动-输入/技能
    TELEPORT_DOUBLE_TAP = true,           -- 是否启用方向键双击触发瞬移
    TELEPORT_DOUBLE_TAP_WINDOW = 0.25,    -- 双击判定窗口(秒)
    TELEPORT_ENABLE_HOTKEY = true,        -- 是否启用快捷键触发瞬移
    TELEPORT_HOTKEY = "R",               -- 快捷键（例如 "R","F","Q","E","SPACE"）

    -- 献祭轮盘配置
    SACRIFICE = {
        ALLOW_DEATH = true,
        LIMITS = {
            DAILY_SPINS_LIMIT = 3,
            COOLDOWN_SECONDS = 30,
            REQUIRE_CONFIRM_FOR_HIGH_RISK = true,
        },
        COSTS = {
            BLOOD_HP_PERCENT = 0.50,
            BLOOD_MIN_HP = 20,
            SOUL_SANITY = 80,
            HUNGER_TO_ZERO = true,
            ITEM_MIN_RARITY = "C",
        },
        PENALTY = {
            FAIL_PROB = 0.10,
            UPPER_CAP_DEBUFF = { blood = {health=-5}, soul={sanity=-10}, hunger={hunger=-15}, item={random=true} },
            LOWER_BOUNDS = { health = 50, sanity = 50, hunger = 100 },
            CURSE_DURATION_DAYS = { lose_blood = 2, heart_demons = 3, gluttony = 2, item_scourge = 2 },
        },
        ODDS = {
            BLOOD = { small=0.60, mid=0.25, big=0.12, super=0.03 },
            SOUL  = { mid=0.50, big=0.30, super=0.15, legend=0.05 },
            HUNGER= { mid=0.40, big=0.35, super=0.20, legend=0.05 },
            -- ITEM：将基于稀有度做偏移
        },
    },
}

-- Difficulty presets (future use)
GLOBAL.TUNING.CARAVAN_DIFF = {
    easy = { favor_mult = 0.9 },
    normal = { favor_mult = 1.0 },
    hard = { favor_mult = 1.15 },
}

-- Strings (en/zh minimal)
GLOBAL.STRINGS.CARAVAN = {
    HELP = [[商旅巡游录可用指令(直接输入，无需斜杠):
chelp - 查看说明
cui - 打开/关闭UI界面 (推荐使用)
cmutators - 查看今日词条
creroll - 发起重掷词条投票（每人每日一次）
ccontracts - 查看当前合约
cdeliver <编号> - 交付合约物品（如: cdeliver 1）
cboons - 查看被动恩惠状态
cboon list - 查看所有被动恩惠
cboon unlock <ID> - 解锁被动恩惠
cboon equip <ID> - 装备被动恩惠
cboon unequip <ID> - 卸下被动恩惠
crep - 查看阵营声望
caravan - 查看商队信息
creapply - 重新应用词条效果到所有玩家（调试用）
cstatus - 检查词条系统状态（调试用）

被动恩惠ID（按职业分类）:
战士系: warrior_speed, warrior_damage, warrior_combo
法师系: mage_poison, mage_charge, mage_teleport
召唤师系: summon_spider, summon_pig, summon_boss
农民系: farmer_growth, farmer_harvest, farmer_blessing

控制台命令(按`键打开控制台):
c_rerolll() - 强制重掷词条，绕过每日限制（测试用）
c_mutators() - 查看当前词条（控制台版本）
c_findmutator("词条名") - 自动寻找特定词条

快捷键: 按 V 键打开/关闭UI界面]],
}

return GLOBAL.TUNING.CARAVAN
