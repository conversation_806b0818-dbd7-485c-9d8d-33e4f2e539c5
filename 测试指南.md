# 合约系统测试指南（控制台 c_xxxx() 版）

本指南用于在开发/联机测试时，通过控制台的 c_xxxx() 风格命令高效验证“合约系统”的主要功能与边界情况。提供两类方式：
- 聊天命令（无需斜杠，直接在聊天框输入）
- 控制台命令（按 ~ 打开控制台）

建议优先用“控制台命令”，因为更适合记录与复现。

## 1. 前置准备
- 开启控制台：游戏设置 → 启用控制台；测试时按 `~` 打开
- 本地主持或专用服务器：建议在主机侧执行（TheNet:GetIsServer() 为 true），否则部分改动无效
- 推荐使用开发世界，避免污染正式存档

## 2. 快速命令总览

已有的聊天命令（在聊天框直接输入，无需 /）：
- 查看当前合约：`ccontracts`
- 交付合约：`cdeliver <编号>`（例如：`cdeliver 1`）

常用的控制台（Console）调用：
- 打印当前合约：
  - `TheWorld.components.modworld_global:CmdShowContracts(ThePlayer)`
- 交付合约（以编号“1”为例）：
  - `TheWorld.components.modworld_global:CmdDeliverContract(ThePlayer, "1")`
- 重新生成全部合约（覆盖当前合约池）：
  - `TheWorld.components.modworld_global:GenerateContracts()`
- 刷新单个合约（内测，含冷却）：
  - `TheWorld.components.modworld_global:RefreshSingleContract(1)`
- 获取合约快照（用于调试/打印）：
  - `TheWorld.components.modworld_global:GetContractsSnapshotForClient(ThePlayer)`

可选：请求服务端回传快照（走 RPC，便于触发 UI 刷新）
- `SendModRPCToServer(GetModRPC("thinking","contract_request"))`

## 3. 一次性定义：控制台快捷函数（推荐）
将以下代码一次性粘贴到控制台，即可拥有 c_xxxx() 形式的测试函数：

```lua
-- 合约：打印、交付、刷新
rawset(_G, "c_contracts", function()
    local w = TheWorld; local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdShowContracts(ThePlayer) end
end)

rawset(_G, "c_deliver", function(id)
    local w = TheWorld; local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdDeliverContract(ThePlayer, tostring(id)) end
end)

rawset(_G, "c_contracts_gen", function()
    local comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    if comp then comp:GenerateContracts() end
end)

rawset(_G, "c_contracts_refresh_one", function(index)
    local comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    if comp then comp:RefreshSingleContract(tonumber(index)) end
end)

rawset(_G, "c_contracts_snapshot", function()
    local comp = TheWorld and TheWorld.components and TheWorld.components.modworld_global
    if comp then
        local s = comp:GetContractsSnapshotForClient(ThePlayer)
        if type(s) == "table" then for k,v in pairs(s) do print(k,v) end else print(s) end
    end
end)
```

之后即可直接用：
- `c_contracts()`
- `c_deliver(1)`
- `c_contracts_gen()`
- `c_contracts_refresh_one(1)`
- `c_contracts_snapshot()`

## 4. 基本测试流程

1) 生成/确认当前合约
- 控制台：`c_contracts_gen()`（可选）
- 控制台：`c_contracts()` 或 聊天：`ccontracts`
- 观察打印内容：每条合约会显示类型、目标、进度、奖励等

2) 进度推进
- 击杀类（kill）：`c_spawn("spider")` 生成目标 → 用角色击杀 → `c_contracts()` 查看进度
- 采集类（collect）：在世界中采集/拾取/收获指定物品 → 查看进度
- 制作类（craft/build）：制作/建造指定配方 → 查看进度
- 交付类（deliver）：准备好背包内所需物品 →`c_deliver(<编号>)` 或 聊天 `cdeliver <编号>`

3) 刷新单条（内测）
- 控制台：`c_contracts_refresh_one(1)` 刷新第1条合约
- 再次 `c_contracts()` 查看是否更换

4) 完成与奖励校验
- 当进度达到目标，系统会标记完成并发放奖励（Favor/声望）
- 控制台可检查 Favor：`print(ThePlayer and ThePlayer.components and ThePlayer.components.modplayer_boons and ThePlayer.components.modplayer_boons.favor)`

## 5. 进阶与边界用例

- 归属判定（击杀）：
  - 召唤物代杀：由玩家随从/召唤物（follower）击杀时，进度归属其领队玩家
  - 环境致死：火烧/冻死/陷阱等环境原因不计入（contract_events.lua 校验）

- 采集归属：
  - 排除调试生成物品、交易/礼物来源、非收获型容器（鸟笼/蜂箱等例外按实现判断）

- 制作归属：
  - 排除“自动制作”与“调试生成结果”

- 去重窗口：
  - 击杀/采集事件 5s 去重；制作事件 1s 去重（contract_events.lua 中的 event_cache）

## 6. 常见问题（FAQ）

- 看不到合约/打印为空？
  - 执行：`c_contracts_gen()` 再 `c_contracts()`，或 `TheWorld.components.modworld_global:EnsureContractsInitialized()`

- 交付失败/无效？
  - 确认命令编号与 `c_contracts()` 列表一致；确认背包内物品数量/新鲜度/耐久度满足合约要求（见 mod 配置）

- 客户端执行无效？
  - 需在主机/服务器端执行；或通过 RPC：`SendModRPCToServer(GetModRPC("thinking","contract_request"))`

- 想触发 UI 刷新？
  - 使用 RPC 请求快照（上面命令）或再次打开 UI（V 键）

## 7. 参考：内置聊天命令（无斜杠）
- `ccontracts`：查看当前合约
- `cdeliver <编号>`：交付指定编号合约
- 其它：`cmutators`（今日词条）、`creroll`（重掷词条）、`caravan`（商队信息）

---
如需将以上 c_xxxx() 函数加入 MOD 内部作为常驻命令，可告知我，我将把它们以 rawset 方式放在 modmain.lua（仅开发环境可启用的调试段）中，以免影响正式玩家体验。
