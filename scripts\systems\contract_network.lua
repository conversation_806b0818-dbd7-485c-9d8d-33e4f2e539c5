-- 合约系统网络通信模块
-- 确保能访问全局变量
local GLOBAL = rawget(_G, "GLOBAL") or _G

local ContractNetwork = {}

-- RPC 命令定义
local RPC_COMMANDS = {
    DELIVER_CONTRACT = "contract_deliver",
    REQUEST_CONTRACTS = "contract_request",
    SYNC_PROGRESS = "contract_sync_progress"
}

-- 分片间通信命令
local SHARD_COMMANDS = {
    PROGRESS_UPDATE = "contract_progress_update",
    CONTRACT_SYNC = "contract_sync",
    CONTRACT_COMPLETE = "contract_complete"
}

-- 初始化网络系统
function ContractNetwork:Init()
    if not GLOBAL.TheNet:GetIsServer() then
        return -- 只在服务端初始化
    end
    
    print("[合约网络] 初始化网络系统")
    
    -- 注册RPC处理器
    self:RegisterRPCs()
    
    -- 注册分片通信
    self:RegisterShardRPCs()
    
    -- 初始化网络变量
    self:InitNetVars()
end

-- 注册RPC处理器
function ContractNetwork:RegisterRPCs()
    -- 交付合约RPC
    GLOBAL.AddModRPCHandler("thinking", RPC_COMMANDS.DELIVER_CONTRACT, function(player, contract_index)
        self:HandleDeliverContract(player, contract_index)
    end)
    
    -- 请求合约数据RPC
    GLOBAL.AddModRPCHandler("thinking", RPC_COMMANDS.REQUEST_CONTRACTS, function(player)
        self:HandleRequestContracts(player)
    end)
    
    -- 同步进度RPC（从其他分片）
    GLOBAL.AddModRPCHandler("thinking", RPC_COMMANDS.SYNC_PROGRESS, function(player, contract_id, progress_delta, event_data)
        self:HandleSyncProgress(player, contract_id, progress_delta, event_data)
    end)
end

-- 分片通信现在通过标准RPC处理，不使用TheShard自定义RPC
function ContractNetwork:RegisterShardRPCs()
    -- 移除TheShard自定义RPC，改为森林分片权威模式
    -- 所有进度更新都通过标准RPC发送到森林分片处理
end

-- 初始化网络变量
function ContractNetwork:InitNetVars()
    local world = GLOBAL.TheWorld
    if not world or not world.net then return end
    
    -- 合约数据网络变量
    world.contract_data = GLOBAL.net_string(world.GUID, "contract_data", "contract_data_dirty")
    world.contract_progress = GLOBAL.net_string(world.GUID, "contract_progress", "contract_progress_dirty")
    world.contract_version = GLOBAL.net_int(world.GUID, "contract_version", "contract_version_dirty")
    
    -- 监听网络变量变化（客户端）
    if not GLOBAL.TheNet:GetIsServer() then
        world:ListenForEvent("contract_data_dirty", function()
            self:OnContractDataChanged()
        end)
        
        world:ListenForEvent("contract_progress_dirty", function()
            self:OnContractProgressChanged()
        end)
    end
end

-- 处理交付合约RPC
function ContractNetwork:HandleDeliverContract(player, contract_index)
    if not player or not contract_index then return end
    
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp then
        print("[合约网络] 错误：无法访问世界组件")
        return
    end
    
    -- 调用原有的交付逻辑
    world_comp:CmdDeliverContract(player, tostring(contract_index))
    
    -- 同步更新到所有客户端
    self:SyncContractsToClients()
end

-- 处理请求合约数据RPC
function ContractNetwork:HandleRequestContracts(player)
    if not player then return end
    
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp then return end
    
    -- 发送合约数据给请求的客户端
    self:SendContractsToPlayer(player, world_comp.contracts)
end

-- 处理进度同步RPC
function ContractNetwork:HandleSyncProgress(player, contract_id, progress_delta, event_data)
    if not self:IsAuthorityServer() then
        -- 如果不是权威服务器，转发到权威分片
        self:ForwardToAuthorityShard({
            command = SHARD_COMMANDS.PROGRESS_UPDATE,
            contract_id = contract_id,
            progress_delta = progress_delta,
            event_data = event_data,
            player_id = player and player.userid or nil
        })
        return
    end
    
    -- 权威服务器处理进度更新
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp then return end
    
    world_comp:UpdateContractProgress(contract_id, progress_delta, event_data)
    
    -- 同步到所有分片和客户端
    self:SyncContractsToAll()
end

-- 分片相关方法已移除，改为森林分片权威模式
-- 所有进度更新通过标准RPC直接发送到森林分片处理

-- 同步合约到客户端
function ContractNetwork:SyncContractsToClients()
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp then return end
    
    local world = GLOBAL.TheWorld
    if not world or not world.contract_data then return end
    
    -- 序列化合约数据
    local contract_json = GLOBAL.json.encode({
        contracts = world_comp.contracts,
        timestamp = GLOBAL.GetTime()
    })
    
    -- 更新网络变量
    world.contract_data:set(contract_json)
    world.contract_version:set((world_comp.contract_version or 0) + 1)
    
    world_comp.contract_version = (world_comp.contract_version or 0) + 1
end

-- 发送合约数据给特定玩家
function ContractNetwork:SendContractsToPlayer(player, contracts)
    if not player or not contracts then return end
    
    -- 通过RPC发送数据（如果数据太大，可以分批发送）
    local contract_data = GLOBAL.json.encode({
        contracts = contracts,
        timestamp = GLOBAL.GetTime()
    })
    
    GLOBAL.SendModRPCToClient(GLOBAL.GetClientTable(player.userid), "thinking", "receive_contracts", contract_data)
end

-- 客户端：合约数据变化处理
function ContractNetwork:OnContractDataChanged()
    local world = GLOBAL.TheWorld
    if not world or not world.contract_data then return end
    
    local contract_json = world.contract_data:value()
    if not contract_json or contract_json == "" then return end
    
    local success, contract_data = pcall(GLOBAL.json.decode, contract_json)
    if not success then
        print("[合约网络] 解析合约数据失败")
        return
    end
    
    -- 通知UI更新
    if GLOBAL.TheFrontEnd and GLOBAL.TheFrontEnd.GetActiveScreen then
        local screen = GLOBAL.TheFrontEnd:GetActiveScreen()
        if screen and screen.name == "CaravanScreen" then
            screen:OnContractDataUpdated(contract_data.contracts)
        end
    end
end

-- 客户端：合约进度变化处理
function ContractNetwork:OnContractProgressChanged()
    -- 类似于OnContractDataChanged的处理
    self:OnContractDataChanged()
end

-- 客户端RPC：发送交付请求
function ContractNetwork:ClientDeliverContract(contract_index)
    if GLOBAL.TheNet:GetIsServer() then return end -- 只在客户端调用

    GLOBAL.SendModRPCToServer(GLOBAL.MOD_RPC["thinking"]["contract_deliver"], contract_index)
end

-- 客户端RPC：请求合约数据
function ContractNetwork:ClientRequestContracts()
    if GLOBAL.TheNet:GetIsServer() then return end

    GLOBAL.SendModRPCToServer(GLOBAL.MOD_RPC["thinking"]["contract_request"])
end

-- 上报进度到权威服务器
function ContractNetwork:ReportProgress(contract_id, progress_delta, event_data)
    if not GLOBAL.TheNet:GetIsServer() then return end
    
    if self:IsAuthorityServer() then
        -- 直接处理
        self:HandleSyncProgress(nil, contract_id, progress_delta, event_data)
    else
        -- 转发到权威分片
        self:ForwardToAuthorityShard({
            command = SHARD_COMMANDS.PROGRESS_UPDATE,
            contract_id = contract_id,
            progress_delta = progress_delta,
            event_data = event_data
        })
    end
end

return ContractNetwork
