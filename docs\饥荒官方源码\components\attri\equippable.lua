---@meta

---@class component_equippable
---@field inst ent
---@field isequipped idk
---@field equipslot EQUIPSLOTS|string # 设置装备槽位
---@field onequipfn fun(this: ent,owner: ent,from_ground: any,...: any): any # 穿戴回调函数
---@field onunequipfn fun(this: ent,owner: ent,...: any): any # 卸除回调函数
---@field onpocketfn idk
---@field onequiptomodelfn fun(this: ent,owner: ent,from_ground: any,...: any): any # 假人穿戴回调函数
---@field equipstack idk
---@field walkspeedmult number # 设置移速倍率,例如+20%移速就是,1.2,注意这里的移速只能有1位小数(忘了一位还是两位了)
---@field restrictedtag string # 有这个tag的玩家才能装备 <br> klei还有个 `self.retrictedtag` 单词拼错了,所以废弃了 <br> 我当时怎么用怎么不生效,我说我脚本扫出来的怎么会有问题呢,没想到吧
---@field dapperness number # 显示 `+多少精神/min`, 这里填的时候注意要 `/54`
---@field dapperfn idk
---@field insulated boolean # 是 则100%防雷
---@field equippedmoisture idk
---@field maxequippedmoisture idk
---@field preventunequipping idk
---@field _onremovelistener idk