-- 合约事件计数系统
-- 实现全覆盖的击杀、采集、制作事件计数和归属判定

local GLOBAL = rawget(_G, "GLOBAL") or _G

local ContractEvents = {}

-- 事件去重缓存
ContractEvents.event_cache = {
    kills = {}, -- {entity_guid: {timestamp, player}}
    collections = {}, -- {item_guid: {timestamp, player}}
    crafts = {}, -- {item_guid: {timestamp, player}}
    cache_duration = 5 -- 5秒去重窗口
}

-- 初始化事件系统
function ContractEvents:Init()
    print("[合约事件] 初始化全覆盖事件计数系统")
    
    -- 设置击杀事件监听
    self:SetupKillEventListeners()
    
    -- 设置采集事件监听
    self:SetupCollectionEventListeners()
    
    -- 设置制作事件监听
    self:SetupCraftEventListeners()
    
    -- 定期清理缓存
    self:SetupCacheCleanup()
end

-- 设置击杀事件监听
function ContractEvents:SetupKillEventListeners()
    print("[合约事件] 设置击杀事件监听")
    
    -- 监听新玩家加入
    if GLOBAL.TheWorld then
        GLOBAL.TheWorld:ListenForEvent("ms_newplayerspawned", function(_, player)
            self:SetupPlayerKillListeners(player)
        end)
        
        -- 为已存在的玩家设置监听
        for _, player in ipairs(GLOBAL.AllPlayers or {}) do
            if player and player:IsValid() then
                self:SetupPlayerKillListeners(player)
            end
        end
        
        -- 监听全局实体死亡事件（备用）
        GLOBAL.TheWorld:ListenForEvent("entity_death", function(_, data)
            if data and data.entity then
                self:OnEntityDeath(data.entity)
            end
        end)
    end
end

-- 为单个玩家设置击杀监听
function ContractEvents:SetupPlayerKillListeners(player)
    if not player or not player:IsValid() then return end
    
    -- 避免重复监听
    if player._contract_kill_listener_setup then return end
    player._contract_kill_listener_setup = true
    
    -- 监听玩家击杀事件
    player:ListenForEvent("killed", function(_, data)
        if data and data.victim then
            self:OnPlayerKill(player, data.victim, data)
        end
    end)
    
    print("[合约事件] 为玩家设置击杀监听:", player.name or "未知玩家")
end

-- 处理玩家击杀事件
function ContractEvents:OnPlayerKill(player, victim, data)
    if not player or not victim or not player:HasTag("player") then return end
    
    -- 验证击杀归属
    local attribution = self:ValidateKillAttribution(player, victim, data)
    if not attribution.valid then return end
    
    -- 去重检查
    if self:IsDuplicateKillEvent(victim, player) then return end
    
    -- 记录事件到缓存
    self:RecordKillEvent(victim, player)
    
    -- 上报进度
    self:ReportKillProgress(attribution.player, victim, data)
    
    print("[合约事件] 击杀事件:", attribution.player.name or "未知玩家", "击杀了", victim.prefab or "未知生物")
end

-- 验证击杀归属
function ContractEvents:ValidateKillAttribution(player, victim, data)
    local result = {valid = false, player = nil, reason = ""}
    
    -- 基础验证
    if not player:HasTag("player") then
        result.reason = "击杀者不是玩家"
        return result
    end
    
    -- 检查召唤物代杀
    if data and data.attacker and data.attacker ~= player then
        local attacker = data.attacker
        
        -- 检查是否为玩家的召唤物
        if attacker.components and attacker.components.follower then
            local leader = attacker.components.follower.leader
            if leader and leader:HasTag("player") then
                result.valid = true
                result.player = leader
                result.reason = "召唤物代杀"
                return result
            end
        end
        
        -- 检查是否为玩家的宠物/随从
        if attacker.summoner and attacker.summoner:HasTag("player") then
            result.valid = true
            result.player = attacker.summoner
            result.reason = "宠物代杀"
            return result
        end
    end
    
    -- 检查陷阱和环境伤害（不计入）
    if self:IsEnvironmentalDeath(victim, data) then
        result.reason = "环境或陷阱致死"
        return result
    end
    
    -- 直接击杀
    result.valid = true
    result.player = player
    result.reason = "直接击杀"
    return result
end

-- 检查是否为环境致死
function ContractEvents:IsEnvironmentalDeath(victim, data)
    if not data then return false end
    
    -- 检查伤害来源
    local damage_source = data.cause or data.damage_source
    if damage_source then
        local environmental_sources = {
            "fire", "freeze", "poison", "starvation", "darkness",
            "trap", "spike", "lightning", "drowning", "falling"
        }
        
        for _, source in ipairs(environmental_sources) do
            if tostring(damage_source):find(source) then
                return true
            end
        end
    end
    
    -- 检查受害者的死亡原因
    if victim.components and victim.components.health then
        local death_cause = victim.components.health.death_cause
        if death_cause and (death_cause:find("fire") or death_cause:find("freeze") or 
                           death_cause:find("trap") or death_cause:find("environment")) then
            return true
        end
    end
    
    return false
end

-- 实体死亡事件处理（备用）
function ContractEvents:OnEntityDeath(entity)
    if not entity or not entity.prefab then return end
    
    -- 查找最近的玩家作为可能的击杀者
    local killer = self:FindNearestPlayerKiller(entity)
    if killer then
        self:OnPlayerKill(killer, entity, {cause = "proximity"})
    end
end

-- 查找最近的玩家击杀者
function ContractEvents:FindNearestPlayerKiller(entity)
    local max_distance = 20 -- 最大距离20格
    local nearest_player = nil
    local min_distance = max_distance
    
    for _, player in ipairs(GLOBAL.AllPlayers or {}) do
        if player and player:IsValid() and player:HasTag("player") then
            local distance = entity:GetDistanceSqToInst(player)
            if distance < min_distance * min_distance then
                min_distance = math.sqrt(distance)
                nearest_player = player
            end
        end
    end
    
    return nearest_player
end

-- 设置采集事件监听
function ContractEvents:SetupCollectionEventListeners()
    print("[合约事件] 设置采集事件监听")

    -- 监听特定的采集事件
    if GLOBAL.TheWorld then
        GLOBAL.TheWorld:ListenForEvent("ms_newplayerspawned", function(_, player)
            self:SetupPlayerCollectionListeners(player)
        end)

        for _, player in ipairs(GLOBAL.AllPlayers or {}) do
            if player and player:IsValid() then
                self:SetupPlayerCollectionListeners(player)
            end
        end
    end
end

-- 为单个玩家设置采集监听
function ContractEvents:SetupPlayerCollectionListeners(player)
    if not player or not player:IsValid() then return end

    if player._contract_collection_listener_setup then return end
    player._contract_collection_listener_setup = true

    -- 监听采集事件
    player:ListenForEvent("picksomething", function(_, data)
        if data and data.object and data.loot then
            for _, item in ipairs(data.loot) do
                self:OnItemCollected(player, item, "pick")
            end
        end
    end)

    -- 监听拾取事件
    player:ListenForEvent("gotnewitem", function(_, data)
        if data and data.item then
            self:OnItemCollected(player, data.item, "pickup")
        end
    end)

    -- 监听收获事件
    player:ListenForEvent("harvested", function(_, data)
        if data and data.product then
            self:OnItemCollected(player, data.product, "harvest")
        end
    end)

    -- 监听完成工作事件（砍伐、挖掘等）
    player:ListenForEvent("finishedwork", function(_, data)
        if data and data.target and data.target.components and data.target.components.workable then
            local product = data.target.components.workable.product
            if product then
                self:OnItemCollected(player, product, "work")
            end
        end
    end)

    print("[合约事件] 为玩家设置采集监听:", player.name or "未知玩家")
end

-- 处理物品采集事件
function ContractEvents:OnItemCollected(player, item, source)
    if not player or not item or not player:HasTag("player") then return end
    
    -- 验证采集归属
    if not self:ValidateCollectionAttribution(player, item, source) then return end
    
    -- 去重检查
    if self:IsDuplicateCollectionEvent(item, player) then return end
    
    -- 记录事件到缓存
    self:RecordCollectionEvent(item, player)
    
    -- 上报进度
    self:ReportCollectionProgress(player, item, source)
    
    print("[合约事件] 采集事件:", player.name or "未知玩家", "采集了", item.prefab or "未知物品")
end

-- 验证采集归属
function ContractEvents:ValidateCollectionAttribution(player, item, source)
    -- 排除调试生成的物品
    if item._was_spawned_by_debug then return false end
    
    -- 排除交易获得的物品
    if source == "trade" or source == "gift" then return false end
    
    -- 排除从容器中取出的物品（除非是收获型容器）
    if source == "container" and not self:IsHarvestContainer(source) then return false end
    
    return true
end

-- 检查是否为收获型容器
function ContractEvents:IsHarvestContainer(container)
    if not container then return false end
    
    local harvest_containers = {"beebox", "farm", "mushroom_farm", "birdcage"}
    for _, harvest_type in ipairs(harvest_containers) do
        if container.prefab and container.prefab:find(harvest_type) then
            return true
        end
    end
    
    return false
end

-- 设置制作事件监听
function ContractEvents:SetupCraftEventListeners()
    print("[合约事件] 设置制作事件监听")

    -- 监听新玩家加入
    if GLOBAL.TheWorld then
        GLOBAL.TheWorld:ListenForEvent("ms_newplayerspawned", function(_, player)
            self:SetupPlayerCraftListeners(player)
        end)

        for _, player in ipairs(GLOBAL.AllPlayers or {}) do
            if player and player:IsValid() then
                self:SetupPlayerCraftListeners(player)
            end
        end
    end
end

-- 为单个玩家设置制作监听
function ContractEvents:SetupPlayerCraftListeners(player)
    if not player or not player:IsValid() then return end

    if player._contract_craft_listener_setup then return end
    player._contract_craft_listener_setup = true

    -- 监听制作事件
    player:ListenForEvent("builditem", function(_, data)
        if data and data.item then
            self:OnItemCrafted(player, data.item, data.item, "craft")
        end
    end)

    -- 监听建造事件
    player:ListenForEvent("buildstructure", function(_, data)
        if data and data.structure then
            self:OnItemCrafted(player, data.structure.prefab, data.structure, "build")
        end
    end)

    print("[合约事件] 为玩家设置制作监听:", player.name or "未知玩家")
end

-- 处理物品制作事件
function ContractEvents:OnItemCrafted(player, recipe, result, craft_type)
    if not player or not recipe or not player:HasTag("player") then return end
    
    -- 验证制作归属
    if not self:ValidateCraftAttribution(player, recipe, result, craft_type) then return end
    
    -- 创建虚拟物品用于去重
    local virtual_item = {
        prefab = type(recipe) == "string" and recipe or recipe.product,
        _craft_timestamp = GLOBAL.GetTime(),
        _crafter = player
    }
    
    -- 去重检查
    if self:IsDuplicateCraftEvent(virtual_item, player) then return end
    
    -- 记录事件到缓存
    self:RecordCraftEvent(virtual_item, player)
    
    -- 上报进度
    self:ReportCraftProgress(player, recipe, craft_type)
    
    print("[合约事件] 制作事件:", player.name or "未知玩家", "制作了", virtual_item.prefab or "未知物品")
end

-- 验证制作归属
function ContractEvents:ValidateCraftAttribution(player, recipe, result, craft_type)
    -- 排除调试制作
    if result and result._was_spawned_by_debug then return false end
    
    -- 排除自动制作（如果有的话）
    if craft_type == "auto" then return false end
    
    return true
end

-- 去重和缓存管理方法

-- 检查是否为重复击杀事件
function ContractEvents:IsDuplicateKillEvent(victim, player)
    if not victim or not victim.GUID then return false end

    local current_time = GLOBAL.GetTime()
    local cache_entry = self.event_cache.kills[victim.GUID]

    if cache_entry then
        local time_diff = current_time - cache_entry.timestamp
        if time_diff < self.event_cache.cache_duration then
            -- 检查是否为同一玩家
            if cache_entry.player == player then
                return true
            end
        end
    end

    return false
end

-- 记录击杀事件到缓存
function ContractEvents:RecordKillEvent(victim, player)
    if not victim or not victim.GUID then return end

    self.event_cache.kills[victim.GUID] = {
        timestamp = GLOBAL.GetTime(),
        player = player,
        victim_prefab = victim.prefab
    }
end

-- 检查是否为重复采集事件
function ContractEvents:IsDuplicateCollectionEvent(item, player)
    if not item or not item.GUID then return false end

    local current_time = GLOBAL.GetTime()
    local cache_entry = self.event_cache.collections[item.GUID]

    if cache_entry then
        local time_diff = current_time - cache_entry.timestamp
        if time_diff < self.event_cache.cache_duration then
            if cache_entry.player == player then
                return true
            end
        end
    end

    return false
end

-- 记录采集事件到缓存
function ContractEvents:RecordCollectionEvent(item, player)
    if not item or not item.GUID then return end

    self.event_cache.collections[item.GUID] = {
        timestamp = GLOBAL.GetTime(),
        player = player,
        item_prefab = item.prefab
    }
end

-- 检查是否为重复制作事件
function ContractEvents:IsDuplicateCraftEvent(virtual_item, player)
    -- 制作事件使用特殊的去重逻辑
    local current_time = GLOBAL.GetTime()
    local cache_key = player.GUID .. "_" .. virtual_item.prefab
    local cache_entry = self.event_cache.crafts[cache_key]

    if cache_entry then
        local time_diff = current_time - cache_entry.timestamp
        if time_diff < 1 then -- 制作事件1秒去重窗口
            return true
        end
    end

    return false
end

-- 记录制作事件到缓存
function ContractEvents:RecordCraftEvent(virtual_item, player)
    if not virtual_item or not player then return end

    local cache_key = player.GUID .. "_" .. virtual_item.prefab
    self.event_cache.crafts[cache_key] = {
        timestamp = GLOBAL.GetTime(),
        player = player,
        item_prefab = virtual_item.prefab
    }
end

-- 设置缓存清理
function ContractEvents:SetupCacheCleanup()
    if not GLOBAL.TheWorld then return end

    -- 每30秒清理一次过期缓存
    GLOBAL.TheWorld:DoPeriodicTask(30, function()
        self:CleanupExpiredCache()
    end)
end

-- 清理过期缓存
function ContractEvents:CleanupExpiredCache()
    local current_time = GLOBAL.GetTime()
    local cleanup_count = 0

    -- 清理击杀事件缓存
    for guid, entry in pairs(self.event_cache.kills) do
        if current_time - entry.timestamp > self.event_cache.cache_duration * 2 then
            self.event_cache.kills[guid] = nil
            cleanup_count = cleanup_count + 1
        end
    end

    -- 清理采集事件缓存
    for guid, entry in pairs(self.event_cache.collections) do
        if current_time - entry.timestamp > self.event_cache.cache_duration * 2 then
            self.event_cache.collections[guid] = nil
            cleanup_count = cleanup_count + 1
        end
    end

    -- 清理制作事件缓存
    for key, entry in pairs(self.event_cache.crafts) do
        if current_time - entry.timestamp > self.event_cache.cache_duration * 2 then
            self.event_cache.crafts[key] = nil
            cleanup_count = cleanup_count + 1
        end
    end

    if cleanup_count > 0 then
        print("[合约事件] 清理了", cleanup_count, "个过期缓存条目")
    end
end

-- 进度上报方法

-- 上报击杀进度
function ContractEvents:ReportKillProgress(player, victim, data)
    if not player or not victim then return end

    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp then return end

    -- 查找匹配的击杀合约
    for _, contract in ipairs(world_comp.contracts or {}) do
        if contract.type == "kill" and not contract.completed then
            if self:DoesTargetMatch(victim, contract.target_data, "kill") then
                -- 通过网络系统上报进度
                if world_comp.network then
                    world_comp.network:ReportProgress(contract.id, 1, {
                        type = "kill",
                        target = victim.prefab,
                        player = player.name or "未知玩家"
                    })
                else
                    -- 直接更新进度（降级方案）
                    world_comp:UpdateContractProgress(contract.id, 1, {
                        type = "kill",
                        target = victim.prefab,
                        player = player.name or "未知玩家"
                    })
                end
                break
            end
        end
    end
end

-- 上报采集进度
function ContractEvents:ReportCollectionProgress(player, item, source)
    if not player or not item then return end

    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp then return end

    -- 计算物品数量
    local count = 1
    if item.components and item.components.stackable then
        count = item.components.stackable:StackSize()
    end

    -- 查找匹配的采集合约
    for _, contract in ipairs(world_comp.contracts or {}) do
        if contract.type == "collect" and not contract.completed then
            if self:DoesTargetMatch(item, contract.target_data, "collect") then
                -- 通过网络系统上报进度
                if world_comp.network then
                    world_comp.network:ReportProgress(contract.id, count, {
                        type = "collect",
                        target = item.prefab,
                        source = source,
                        player = player.name or "未知玩家"
                    })
                else
                    -- 直接更新进度（降级方案）
                    world_comp:UpdateContractProgress(contract.id, count, {
                        type = "collect",
                        target = item.prefab,
                        source = source,
                        player = player.name or "未知玩家"
                    })
                end
                break
            end
        end
    end
end

-- 上报制作进度
function ContractEvents:ReportCraftProgress(player, recipe, craft_type)
    if not player or not recipe then return end

    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp then return end

    local recipe_name = type(recipe) == "string" and recipe or recipe.product

    -- 查找匹配的制作合约
    for _, contract in ipairs(world_comp.contracts or {}) do
        if contract.type == "craft" and not contract.completed then
            if self:DoesTargetMatch({prefab = recipe_name}, contract.target_data, "craft") then
                -- 通过网络系统上报进度
                if world_comp.network then
                    world_comp.network:ReportProgress(contract.id, 1, {
                        type = "craft",
                        target = recipe_name,
                        craft_type = craft_type,
                        player = player.name or "未知玩家"
                    })
                else
                    -- 直接更新进度（降级方案）
                    world_comp:UpdateContractProgress(contract.id, 1, {
                        type = "craft",
                        target = recipe_name,
                        craft_type = craft_type,
                        player = player.name or "未知玩家"
                    })
                end
                break
            end
        end
    end
end

-- 检查目标是否匹配合约
function ContractEvents:DoesTargetMatch(entity, target_data, contract_type)
    if not entity or not target_data then return false end

    if contract_type == "kill" then
        return entity.prefab == target_data.prefab
    elseif contract_type == "collect" then
        return entity.prefab == target_data.item
    elseif contract_type == "craft" then
        return entity.prefab == target_data.item
    end

    return false
end

-- 获取事件统计信息
function ContractEvents:GetEventStats()
    local stats = {
        kills_cached = 0,
        collections_cached = 0,
        crafts_cached = 0,
        cache_duration = self.event_cache.cache_duration
    }

    for _ in pairs(self.event_cache.kills) do
        stats.kills_cached = stats.kills_cached + 1
    end

    for _ in pairs(self.event_cache.collections) do
        stats.collections_cached = stats.collections_cached + 1
    end

    for _ in pairs(self.event_cache.crafts) do
        stats.crafts_cached = stats.crafts_cached + 1
    end

    return stats
end

-- 重置事件缓存
function ContractEvents:ResetEventCache()
    self.event_cache = {
        kills = {},
        collections = {},
        crafts = {},
        cache_duration = 5
    }
    print("[合约事件] 事件缓存已重置")
end

return ContractEvents
