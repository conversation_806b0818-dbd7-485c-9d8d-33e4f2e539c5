-- 合约生命周期管理系统
-- 实现每日刷新、当日完成不替换等机制

local GLOBAL = rawget(_G, "GLOBAL") or _G

local ContractLifecycle = {}

-- 生命周期状态
ContractLifecycle.state = {
    current_day = -1,
    last_refresh_day = -1,
    daily_contracts = {},
    completed_today = {},
    refresh_pending = false
}

-- 初始化生命周期系统
function ContractLifecycle:Init()
    print("[合约生命周期] 初始化生命周期管理系统")
    
    -- 设置日期变化监听
    self:SetupDayChangeListeners()
    
    -- 初始化当前状态
    self:InitializeCurrentState()
    
    -- 设置定期检查
    self:SetupPeriodicChecks()
end

-- 设置日期变化监听
function ContractLifecycle:SetupDayChangeListeners()
    if not GLOBAL.TheWorld then return end
    
    -- 监听日期变化
    GLOBAL.TheWorld:ListenForEvent("cycleschanged", function()
        self:OnDayChanged()
    end)
    
    -- 监听世界状态变化
    GLOBAL.TheWorld:ListenForEvent("clocktick", function()
        self:OnClockTick()
    end)
    
    print("[合约生命周期] 设置日期变化监听")
end

-- 初始化当前状态
function ContractLifecycle:InitializeCurrentState()
    local current_cycles = self:GetCurrentDay()
    
    self.state.current_day = current_cycles
    self.state.last_refresh_day = current_cycles
    
    print("[合约生命周期] 初始化状态，当前天数:", current_cycles)
end

-- 设置定期检查
function ContractLifecycle:SetupPeriodicChecks()
    if not GLOBAL.TheWorld then return end
    
    -- 每分钟检查一次状态
    GLOBAL.TheWorld:DoPeriodicTask(60, function()
        self:PeriodicStateCheck()
    end)
    
    -- 每10分钟进行一次完整性检查
    GLOBAL.TheWorld:DoPeriodicTask(600, function()
        self:IntegrityCheck()
    end)
end

-- 获取当前天数
function ContractLifecycle:GetCurrentDay()
    if GLOBAL.TheWorld and GLOBAL.TheWorld.state then
        return GLOBAL.TheWorld.state.cycles or 0
    end
    return 0
end

-- 日期变化处理
function ContractLifecycle:OnDayChanged()
    local new_day = self:GetCurrentDay()
    local old_day = self.state.current_day
    
    print("[合约生命周期] 日期变化:", old_day, "->", new_day)
    
    if new_day > old_day then
        self.state.current_day = new_day
        self:ProcessDayTransition(old_day, new_day)
    end
end

-- 处理日期转换
function ContractLifecycle:ProcessDayTransition(old_day, new_day)
    print("[合约生命周期] 处理日期转换:", old_day, "->", new_day)
    
    -- 检查是否需要刷新合约
    if self:ShouldRefreshContracts(new_day) then
        self:ScheduleContractRefresh(new_day)
    end
    
    -- 清理昨日完成记录
    self:CleanupCompletedRecords(old_day)
    
    -- 更新合约状态
    self:UpdateContractStates(new_day)
end

-- 检查是否应该刷新合约
function ContractLifecycle:ShouldRefreshContracts(current_day)
    -- 每日刷新
    if current_day > self.state.last_refresh_day then
        return true
    end
    
    -- 如果有待刷新标记
    if self.state.refresh_pending then
        return true
    end
    
    return false
end

-- 安排合约刷新
function ContractLifecycle:ScheduleContractRefresh(new_day)
    print("[合约生命周期] 安排合约刷新，目标天数:", new_day)
    
    -- 延迟执行刷新，确保世界状态稳定
    if GLOBAL.TheWorld then
        GLOBAL.TheWorld:DoTaskInTime(5, function()
            self:ExecuteContractRefresh(new_day)
        end)
    end
    
    self.state.refresh_pending = true
end

-- 执行合约刷新
function ContractLifecycle:ExecuteContractRefresh(target_day)
    print("[合约生命周期] 执行合约刷新，天数:", target_day)
    
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp then
        print("[合约生命周期] 警告：无法访问世界组件")
        return
    end
    
    -- 保存当日完成的合约
    self:SaveCompletedContracts(world_comp.contracts)
    
    -- 生成新合约
    world_comp:GenerateContracts()
    
    -- 更新状态
    self.state.last_refresh_day = target_day
    self.state.refresh_pending = false
    self.state.daily_contracts = self:CopyContracts(world_comp.contracts)
    
    -- 同步到网络
    if world_comp.network then
        world_comp.network:SyncContractsToAll()
    end
    
    print("[合约生命周期] 合约刷新完成")
end

-- 保存已完成的合约
function ContractLifecycle:SaveCompletedContracts(contracts)
    if not contracts then return end
    
    local completed_contracts = {}
    for _, contract in ipairs(contracts) do
        if contract.completed then
            table.insert(completed_contracts, {
                id = contract.id,
                type = contract.type,
                target_data = contract.target_data,
                completed_day = self.state.current_day,
                reward_favor = contract.reward_favor,
                reward_rep = contract.reward_rep
            })
        end
    end
    
    self.state.completed_today = completed_contracts
    print("[合约生命周期] 保存了", #completed_contracts, "个已完成合约")
end

-- 复制合约数据
function ContractLifecycle:CopyContracts(contracts)
    if not contracts then return {} end
    
    local copied = {}
    for _, contract in ipairs(contracts) do
        table.insert(copied, {
            id = contract.id,
            type = contract.type,
            target_data = contract.target_data,
            progress = contract.progress,
            goal = contract.goal,
            reward_favor = contract.reward_favor,
            reward_rep = contract.reward_rep,
            expires = contract.expires,
            completed = contract.completed,
            created_day = contract.created_day,
            difficulty = contract.difficulty
        })
    end
    
    return copied
end

-- 清理完成记录
function ContractLifecycle:CleanupCompletedRecords(old_day)
    -- 保留最近3天的完成记录
    local cutoff_day = old_day - 3
    
    local cleaned_records = {}
    for _, record in ipairs(self.state.completed_today) do
        if record.completed_day > cutoff_day then
            table.insert(cleaned_records, record)
        end
    end
    
    local removed_count = #self.state.completed_today - #cleaned_records
    self.state.completed_today = cleaned_records
    
    if removed_count > 0 then
        print("[合约生命周期] 清理了", removed_count, "个过期完成记录")
    end
end

-- 更新合约状态
function ContractLifecycle:UpdateContractStates(new_day)
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp or not world_comp.contracts then return end
    
    local updated_count = 0
    
    for _, contract in ipairs(world_comp.contracts) do
        -- 更新创建日期（如果缺失）
        if not contract.created_day then
            contract.created_day = new_day
            updated_count = updated_count + 1
        end
        
        -- 检查过期合约（如果有过期机制）
        if contract.expires and contract.expires > 0 and new_day > contract.expires then
            if not contract.completed then
                contract.expired = true
                updated_count = updated_count + 1
            end
        end
    end
    
    if updated_count > 0 then
        print("[合约生命周期] 更新了", updated_count, "个合约状态")
    end
end

-- 时钟滴答处理
function ContractLifecycle:OnClockTick()
    -- 检查是否跨日
    local current_day = self:GetCurrentDay()
    if current_day ~= self.state.current_day then
        self:OnDayChanged()
    end
end

-- 定期状态检查
function ContractLifecycle:PeriodicStateCheck()
    local current_day = self:GetCurrentDay()
    
    -- 检查状态一致性
    if current_day ~= self.state.current_day then
        print("[合约生命周期] 检测到状态不一致，触发同步")
        self:OnDayChanged()
    end
    
    -- 检查是否有待处理的刷新
    if self.state.refresh_pending and current_day > self.state.last_refresh_day then
        print("[合约生命周期] 检测到待处理刷新，执行刷新")
        self:ExecuteContractRefresh(current_day)
    end
end

-- 完整性检查
function ContractLifecycle:IntegrityCheck()
    print("[合约生命周期] 执行完整性检查")
    
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if not world_comp then return end
    
    local issues_found = 0
    
    -- 检查合约数量
    local expected_count = (GLOBAL.TUNING and GLOBAL.TUNING.CARAVAN and GLOBAL.TUNING.CARAVAN.CONTRACT_COUNT) or 3
    if #world_comp.contracts < expected_count then
        print("[合约生命周期] 警告：合约数量不足", #world_comp.contracts, "/", expected_count)
        issues_found = issues_found + 1
    end
    
    -- 检查合约有效性
    for i, contract in ipairs(world_comp.contracts) do
        if not contract.id or not contract.type or not contract.target_data then
            print("[合约生命周期] 警告：发现无效合约", i)
            issues_found = issues_found + 1
        end
        
        if contract.progress < 0 or contract.progress > contract.goal * 2 then
            print("[合约生命周期] 警告：合约进度异常", contract.id, contract.progress)
            issues_found = issues_found + 1
        end
    end
    
    -- 检查日期一致性
    local current_day = self:GetCurrentDay()
    if math.abs(current_day - self.state.current_day) > 1 then
        print("[合约生命周期] 警告：日期状态不一致", current_day, "vs", self.state.current_day)
        issues_found = issues_found + 1
    end
    
    if issues_found > 0 then
        print("[合约生命周期] 完整性检查发现", issues_found, "个问题")
    else
        print("[合约生命周期] 完整性检查通过")
    end
end

-- 手动触发刷新
function ContractLifecycle:ForceRefresh()
    local current_day = self:GetCurrentDay()
    print("[合约生命周期] 手动触发刷新")
    
    self.state.refresh_pending = true
    self:ExecuteContractRefresh(current_day)
end

-- 获取生命周期状态
function ContractLifecycle:GetLifecycleState()
    return {
        current_day = self.state.current_day,
        last_refresh_day = self.state.last_refresh_day,
        daily_contracts_count = #self.state.daily_contracts,
        completed_today_count = #self.state.completed_today,
        refresh_pending = self.state.refresh_pending
    }
end

-- 检查合约是否为当日完成
function ContractLifecycle:IsCompletedToday(contract_id)
    for _, record in ipairs(self.state.completed_today) do
        if record.id == contract_id and record.completed_day == self.state.current_day then
            return true
        end
    end
    return false
end

-- 获取当日完成的合约
function ContractLifecycle:GetTodayCompletedContracts()
    local today_completed = {}
    for _, record in ipairs(self.state.completed_today) do
        if record.completed_day == self.state.current_day then
            table.insert(today_completed, record)
        end
    end
    return today_completed
end

-- 重置生命周期状态
function ContractLifecycle:ResetLifecycleState()
    self.state = {
        current_day = self:GetCurrentDay(),
        last_refresh_day = -1,
        daily_contracts = {},
        completed_today = {},
        refresh_pending = false
    }
    print("[合约生命周期] 生命周期状态已重置")
end

return ContractLifecycle
