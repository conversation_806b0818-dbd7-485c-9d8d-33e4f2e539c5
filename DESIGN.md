# 商旅巡游录 | Caravan Cycle: A+B 综合玩法蓝图（纯代码版）

> 目标：整合 组合A（每日词条+任务合约+被动恩惠）与 组合B（Boss进化+献祭轮盘+商队巡游），仅用代码与文本交互实现，无需新增贴图/音效/UI 资源。

## 一、核心循环
1. 每日黎明 → 随机生成 2 条全局词条（正向/负向各 1），玩家可 /reroll 发起投票每日限 1 次。
2. 世界存在一组可重复“远征合约”（任务） → 完成得 Favor（恩惠代币）。
3. 玩家使用 Favor 兑换/解锁“被动恩惠”（可装备上限 2）。
4. 献祭轮盘系统 → 消耗生命值、理智值、饥饿值或珍贵物品进行极高风险的随机抽奖。
5. 定期出现商队 → 以给予/容器方式回收资源换 Favor/蓝图。
6. Boss 根据全服行为进化（次数、词条强度、季节） → 战斗挑战与收益提升。

## 二、系统模块
### 2.1 每日词条（Daily Mutators）
- 触发：ListenForEvent("cycleschanged")（官方日更事件）。
- 规则：
  - 全服共享：同一世界当天所有玩家看到相同词条
  - 持久化：词条与 current_day 一起保存，读档后不变；仅在天数变化时重新生成
  - 每日随机 N 条（可配置、且不重复）从词条池选择
  - cmutators 查看今日词条；creroll 每人每日一次重掷（消耗个人次数）
- 词条类型与示例：
  - ✓ 正向：丰收之日（采集额外+1）、疾风步伐（移速大幅提升）、澄澈心智（理智不下降）
  - ✗ 负向：脆弱工具（耐久消耗翻倍）、不眠之夜（夜晚理智快速流失）、怪物磁铁（更易被发现）
  - ◈ 中性：玻璃大炮（攻击翻倍但生命减半）、夜猫子（夜晚加成白天虚弱）
  - ★ 事件：商人造访（特殊商队到访）
- 设计理念：符合饥荒整数数值系统，效果明显有冲击力，避免小数百分比。
- 实现：世界组件保存 mutators 与 current_day；玩家组件保存 last_reroll_day 与 reroll_used_today；UI 提供一键重掷。
- 效果系统：MutatorEffects 系统实现真实游戏机制，使用官方 API（locomotor、hunger、sanity、finiteuses 等）。

### 2.2 远征合约（Expedition Contracts）
- 存在形式：世界组件维护任务池（3–5 个同时存在）。
- 任务类型：
  - 击杀类：击杀触手 X 只、蜘蛛 X 只。
  - 交付类：给予猪王 X 物品、上交木板/石砖。
  - 建设类：修复/新建 X 个猪屋、路灯（用原生建造事件计数）。
  - 生态类：植树 X 棵、清理树精 X 次（击败树精）。
- 奖励：Favor n、少量随机蓝图几率。
- 命令：/contracts 查看、/contract <id> 接取（可选，默认共享进度）。
- 完成广播：TheNet:Announce，记录完成时间用于刷新。

### 2.3 被动恩惠（Passive Boons）
- 获取：消耗 Favor 解锁；装备上限 = 2（可配置）。
- 示例：
  - 轻装快行：+5% 移速（不叠加与坐骑）。
  - 匠心耐久：工具耐久消耗 -10%。
  - 夜行心定：夜晚理智损失 -20%。
  - 饥不择食：食物边际饱食更高（阈值调整）。
- 洗点：/boons respec（消耗 Favor），或有限次免费洗点（世界计次）。
- 数据：玩家组件保存“已解锁列表、已装备 slots、可用Favor”。

### 2.4 献祭轮盘（Sacrifice Wheel）
- 核心机制：消耗玩家的生命值、理智值、饥饿值或珍贵物品进行极高风险的随机抽奖。
- 轮盘类型与代价：
  - **血祭轮盘**：消耗50%当前生命值（最少50点）
    - 60%小奖励，25%中等奖励，12%大奖，3%超级大奖
  - **魂祭轮盘**：消耗最少80点理智值
    - 50%中等奖励，30%大奖，15%超级大奖，5%传说奖励
  - **饥祭轮盘**：消耗全部饥饿值（降至0，最少100）
    - 40%中等奖励，35%大奖，20%超级大奖，5%传说奖励
  - **物祭轮盘**：消耗物品，动态识别稀有度等级
    - C级物品（常见易得）：60%小奖励，30%中等奖励，10%大奖
    - B级物品（需准备风险）：40%中等奖励，35%大奖，20%超级大奖，5%传说奖励
    - A级物品（季节/低概率）：30%大奖，40%超级大奖，25%传说奖励，5%神话奖励
    - S级物品（Boss/一次性）：20%超级大奖，50%传说奖励，30%神话奖励
- 奖励池（按稀有度列出不同类型，实际从各自池中随机1项）：
  - 资源奖励抽取规则（统一适用于所有档位的“给资源”类奖励）：
    - 资源池映射：
      - 小奖励→从 C 级资源池抽取；中等奖励→B 级；大奖→A 级；超级→A/S 级；传说/神话→S 级
    - 抽取种类数：
      - 小：1–2 种；中：1–2 种；大：1–2 种；超/传/神：1 种
    - 数量计算：数量 = Base(prefab) × Rand[min,max]，向下取整并按堆叠规则合并
      - 若物品可堆叠：Base(prefab) = 该物品堆叠上限 × 档位系数；系数（C=0.5，B=0.75，A=1.0，S=1.25）
      - 若不可堆叠：Base(prefab)=1；中/大/超/传/神档位分别以 0.2/0.3/0.5/0.6/0.7 概率+1 件
      - Rand[min,max] 取值：小= [1.0,1.5]；中= [1.5,2.0]；大= [2.0,2.5]；超= [2.5,3.0]；传= [3.0,3.5]；神= [3.5,4.0]
    - 选择权重：按照动态稀有度系统（S/A/B/C）评分；同一稀有度内按“基础表分+标签/组件/名称启发”综合权重抽取
    - 过滤与兜底：
      - 黑名单排除不适合作为“资源”的物品（装备/一次性神器等）；可由服务器配置
      - 未识别的新物品默认 C 级，但若带有 boss_drop/ancient/magic 等标签会被抬升
    - 说明：本节所有示例中的“固定资源数量”仅为展示，实际发放一律按该规则动态抽取与结算。

  - 小奖励（50种）：
    1-23) C级资源抽取（从稀有度系统C级池随机1-2种）
    24) 星尘(Favor)+10
    25) 星尘(Favor)+15
    26) 临时buff：移速+10% 持续60秒
    27) 临时buff：攻击+10% 持续60秒
    28) 临时buff：采集效率+10% 持续60秒
    29) 临时buff：夜视 持续60秒
    30) 临时buff：防雨 持续60秒
    31) 临时buff：保温 持续60秒
    32) 临时buff：降温 持续60秒
    33) 即时恢复：生命+20
    34) 即时恢复：理智+30
    35) 即时恢复：饥饿+40
    36) 砍树效率+10% 持续60秒
    37) 采矿效率+10% 持续60秒
    38) 工具耐久消耗-10% 持续60秒
    39) 防御+5% 持续60秒
    40) 负重减轻 持续60秒
    41) 夜间理智损失-50% 持续60秒
    42) 微光伴随 持续60秒
    43) 临时召唤：友好兔人1只 45秒
    44) 临时召唤：友好猪人1只 45秒
    45) 幸运加成+2% 持续5分钟
    46) 基础蓝图1个（原版基础科技）
    47) 临时buff：工作效率+5% 持续90秒
    48) 临时buff：战斗经验+10% 持续60秒
    49) 临时buff：食物效果+10% 持续60秒
    50) 临时buff：理智恢复+20% 持续60秒

  - 中等奖励（30种）：
    1-11) B级资源抽取（从稀有度系统B级池随机1-2种）
    12) 星尘(Favor)+30
    13) 星尘(Favor)+40
    14) 临时buff：移速+20% 持续120秒
    15) 临时buff：攻击+25% 持续60秒
    16) 临时buff：采集效率+25% 持续120秒
    17) 临时护盾：减伤20% 持续60秒
    18) 临时保鲜：食物不腐烂 持续120秒
    19) 即时恢复：生命+50
    20) 即时恢复：理智+80
    21) 即时恢复：饥饿+100
    22) 中级蓝图1个（原版中级科技）
    23) 召唤：友好蜘蛛战士2只 持续60秒
    24) 召唤：友好猪人护卫1只 持续90秒
    25) 地图揭示：小范围
    26) 临时耐久保护：工具耐久消耗-50% 持续120秒
    27) 地图揭示：中等范围
    28) 临时buff：工作效率+15% 持续120秒
    29) 临时buff：战斗经验+20% 持续90秒
    30) 临时buff：食物效果+25% 持续120秒

  - 大奖（20种）：
    1-3) A级资源抽取（从稀有度系统A级池随机1-2种）
    4) 星尘(Favor)+120
    5) 星尘(Favor)+160
    6) 永久+5 生命上限
    7) 永久+5 理智上限
    8) 永久+5 饥饿上限
    9) 高级蓝图1个（原版高级科技）
    10) 召唤：友好猎犬2只 持续90秒
    11) 召唤：友好猪人2只 持续90秒
    12) 临时光环：队友移速+10% 范围6格 持续120秒
    13) 临时光环：队友攻击+10% 范围6格 持续60秒
    14) 临时光环：队友减伤10% 范围6格 持续60秒
    15) 地图揭示：大范围
    16) 临时buff：移速+30% 持续180秒
    17) 临时buff：攻击+40% 持续90秒
    18) 临时buff：采集效率+40% 持续180秒
    19) 临时buff：工具耐久消耗-75% 持续180秒
    20) 临时buff：全属性恢复速度+50% 持续180秒

  - 超级大奖（10种）：
    1) A/S级资源抽取（从稀有度系统A/S级池随机1种）
    2) 永久+20 生命上限
    3) 永久+20 理智上限
    4) 永久+20 饥饿上限
    5) 召唤：友好巨鹿（弱化版）持续60秒
    6) 召唤：友好熊獾（弱化版）持续60秒
    7) 临时强化：全属性恢复速度+100% 持续300秒
    8) 临时护佑：免疫环境伤害（雨/热/冷/黑暗）持续180秒
    9) 顶级蓝图1个（原版顶级科技）
    10) 临时buff：全能力+50% 持续300秒

  - 传说奖励（5种）：
    1) S级资源抽取（从稀有度系统S级池随机1种）
    2) 永久+50 生命上限
    3) 永久+50 理智上限
    4) 永久+50 饥饿上限
    5) 超强护佑：视野+夜视+防雨+保温+降温 持续300秒

  - 神话奖励（3种）：
    1) 终极特权：献祭中奖率+20%（持续1个季节）
    2) 终极强化：永久三维上限各+50，并获得“幸运之神”称号（仅一次可叠加）
    3) 神话领域：范围护盾+移速+采集效率 持续180秒
- 失败惩罚：10%概率额外惩罚（从以下10种中随机1种）：
  1) 永久减少对应三维上限（血祭-5生命，魂祭-10理智，饥祭-15饥饿）
  2) 背包物品全部掉落（装备物品除外）
  3) 体温异常：持续过热或过冷10秒（无法通过装备缓解）
  4) 召唤敌对生物：猎犬群2-3只或蜘蛛战士3-4只
  5) 工具损坏：随机损坏背包中1-2件工具（耐久归零）
  6) 负面光环：移速-30%，攻击-20% 持续120秒
  7) 理智崩溃：强制降至0理智并持续疯狂状态20秒
  8) 饥饿诅咒：食物效果-75% 持续1天
  9) 黑暗诅咒：失去夜视能力，光源效果-50% 持续1天
  10) 随机传送：被传送到地图随机位置（可能是危险区域）
- 死亡风险：血祭轮盘可能直接死亡，其他轮盘也有严重后果
- 上限惩罚：失败时可能永久减少对应属性上限（血祭-5生命上限，魂祭-10理智上限，饥祭-15饥饿上限）
- 命令：/sacrifice 查看轮盘状态，/spin <blood/soul/hunger/item> 进行献祭抽奖。

### 2.5 商队巡游（Caravans）
- 刷新：默认每 7 天 1 次，在生物群系边缘生成“商队点”（使用现有猪/猫单位作为商贩）。
- 交易：给予/容器交互，回收冗余资源（齿轮、宝石、种子、草枝）换 Favor/蓝图。
- 公告：出现时全图公告+近距离 say() 提示；/caravan 查看大致位置（方向提示）。
- 进阶：v2 随机路线巡游与“停靠点”短暂停留（公告提示）。

### 2.6 Boss 进化（Boss Evolutions）
- 触发因子：
  - 季节（Season）、全服累计击杀次数、当日词条标签（如“寒潮”令巨鹿多一次踩地震）。
- v1 覆盖 Boss：巨鹿 Deerclops、海象群 MacTusk。
- 行为变体示例：
  - 巨鹿：周期性踩地震（低强度），可震落玩家手持工具；冰锥落点更分散。
  - 海象：夜间“侦察吼叫”引来 1 个增援猎犬或提高追踪距离一小段时间。
- 奖励：进化 Boss 掉落表增加小概率额外蓝图或 Favor 包。
- 实现：AddPrefabPostInit 注入事件/组件参数而非替换脑树；倍率可配置并可关。

## 三、命令与交互
### 3.1 UI界面（推荐）
- 快捷键：V 键打开/关闭（仅非聊天输入状态）
- 聊天命令：cui 打开/关闭
- 布局：
  - 顶部：标题“商旅巡游录”
  - 中上：内容文本区域（500x300）
  - 底部：标签页（词条/合约/恩惠/献祭/商队），操作按钮区域位于其上（如“重掷词条”）
- 说明：
  - 已移除窗口底部的“刷新/关闭”栏，界面更简洁
  - 词条页提供“重掷词条”按钮，按钮状态与玩家当日次数联动
  - 不再通过聊天输入 v 开关UI，避免与聊天冲突

### 3.2 聊天命令（备用）
- chelp：显示本模组指令与简要说明
- cmutators, creroll：词条相关
- ccontracts：合约相关
- cboons：被动恩惠相关
- csacrifice：献祭轮盘相关
- ccaravan：商队相关

## 四、数据结构与保存
- 世界组件 modworld_global（inst = TheWorld）
  - mutators: [{id, type, ...}] 当日激活的词条
  - current_day: number 记录词条对应的游戏天数
  - contracts: {id => {type, target, progress, goal, reward, expires}}
  - caravan: {next_day, last_spawn_pos}
  - boss_state: {deerclops = {evo, kills}, mactusk = {evo, kills}}
- 玩家组件 modplayer_boons
  - favor: number
  - unlocked_boons: set
  - equipped_boons: [slot1, slot2]
- 玩家组件 modplayer_sacrifice
  - sacrifice_stats: {blood_spins, soul_spins, hunger_spins, item_spins, total_wins}
  - last_sacrifice_day: number 上次献祭的天数
  - curse_effects: [] 当前诅咒效果列表
  - permanent_bonuses: {health, sanity, hunger} 永久属性加成
- 玩家组件 modplayer_reroll（新增）
  - last_reroll_day: number 上次重掷所在天数
  - reroll_used_today: bool 今日是否已重掷

- 持久化：
  - 世界 OnSave/OnLoad：保存 mutators 与 current_day，读档后不被初始化覆盖
  - 玩家 OnSave/OnLoad：保存上面各自字段，进出存档后保持一致

## 五、配置项（modinfo.lua）
- 词条数量（1–3）与类别开关
- 合约并发数与刷新间隔
- Favor 奖励倍率、被动上限
- 献祭轮盘概率调整与奖励倍率
- 献祭代价倍率与每日献祭次数限制
- 商队周期与交易倍率
- Boss 进化开关与强度倍率

## 六、职业被动恩惠系统详细设计

### 6.1 系统概述
职业被动恩惠系统是商旅巡游录的核心玩法之一，为玩家提供四种不同的职业发展方向：
- **斗士系（原战士）**：专注于近战物理输出，提供直接的战斗强化
- **秘法系（原法师）**：专注于魔法技能输出，需要操作技巧和策略
- **巨兽系（原召唤师）**：专注于召唤生物协战，提供策略性玩法
- **农民系**：专注于农业生产和采集强化，提供和平发展路径

### 6.2 斗士系 (Warrior) - 近战物理输出

#### 战士疾行 (warrior_speed)
- **效果**：移动速度提升20%
- **费用**：25恩惠
- **实现**：使用locomotor组件的SetExternalSpeedMultiplier
- **设计理念**：基础强化，提升战士的机动性

#### 战士之力 (warrior_damage)
- **效果**：攻击力提升30%
- **费用**：35恩惠
- **实现**：使用combat组件的externaldamagemultipliers系统
- **设计理念**：直接的伤害提升，体现战士的暴力输出

#### 连击专精 (warrior_combo)
- **效果**：连续攻击同一目标时伤害递增，最多5层，每层+20%（最高2.0x）
- **费用**：50恩惠
- **实现**：
  - doattack 时按叠层设置 combat.externaldamagemultipliers 的临时加成（同帧移除）
  - onattackother 成功命中后移除临时加成，并为该目标叠层；3秒未命中则清空叠层
- **机制**：仅对同一目标叠加，切换目标会清空旧目标叠层
- **设计理念**：奖励专注攻击，增加战斗深度

### 6.3 秘法系 (Mage) - 魔法技能输出

#### 毒素掌控 (mage_poison)
- **效果**：攻击附带毒伤，每秒造成10点伤害，持续5秒
- **费用**：40恩惠
- **实现**：监听onattackother事件，对目标应用DoPeriodicTask
- **特效**：毒伤音效，"中毒了!"提示
- **设计理念**：持续伤害，适合风筝战术

#### 蓄力打击 (mage_charge)
- **效果**：站定不动达 1/3/5 秒阈值时，下次命中分别提升 50%/200%/500%（倍率 x1.5/x3/x6）
- **费用**：60恩惠
- **实现**：
  - 监听 locomote 重置站定起点；onattackother 计算阈值并通过 combat.externaldamagemultipliers 临时加成（同帧移除）
- **机制**：移动会重置计时；站定越久，下一击越强
- **设计理念**：站桩技巧型玩法，更清晰的阈值反馈

#### 闪现术 (mage_teleport)
- **效果**：双击方向键/WSAD或按下自定义快捷键后，瞬移到鼠标位置（最多 8 格），30 秒冷却
- **费用**：80恩惠
- **实现**：
  - 客户端输入监听：TheInput:AddKeyDownHandler（仅当装备“闪现术”时注册）；支持 TUNING.CARAVAN.TELEPORT_DOUBLE_TAP 与 TELEPORT_HOTKEY
  - 客户端发起 RPC：SendModRPCToServer(GetModRPC("thinking","boons_action"), "teleport", x, z)
  - 服务端校验与执行：验证已装备与冷却，夹紧最大距离，Map:GetTileAtPoint 检查地形，Physics:Teleport 执行
- **设计理念**：战术位移，客户端轻触发，服务端权威校验，适配多人

### 6.4 巨兽系 (Summoner) - 召唤生物协战

#### 蜘蛛召唤 (summon_spider)
- **效果**：召唤2只友好蜘蛛战士协助战斗
- **费用**：45恩惠
- **持续时间**：120秒
- **实现**：生成spider_warrior，设置友好标签和跟随关系
- **设计理念**：基础召唤物，提供持续的战斗支援

#### 巨兽召唤 (summon_boss)
- **效果**：召唤1只小型巨鹿协助战斗（体型缩放至50%）
- **费用**：120恩惠
- **持续时间**：60秒
- **实现**：生成deerclops，设置友好随从与跟随关系；缩放处理
- **复活**：死亡后3分钟复活同类；初次与复活均随机从“调侃语句池A”说一句（不固定台词）
- **设计理念**：短时间强力支援，清晰的时间与强度边界

#### 未知契约 (summon_random)
- 效果：随机召唤1个敌对生物（随机池=“所有敌怪”，包含其他模组的敌怪）；设置为友好随从并跟随玩家
- 费用：90恩惠
- 持续时间：120秒（可配置）
- 特殊机制：
  - Boss类型（带 epic/largecreature 标签）被削弱：生命值下调50%且上限5000，攻击力下调50%
  - 死亡复活：小怪1分钟、Boss 3分钟；每次复活都会“重新抽取”随机对象（真正随机池）
  - 角色台词：初次与复活均从“调侃语句池”随机一条（不再固定“金色传说”文案）
    - 示例：你先顶上，我在后面看着；别慌，跟紧我就行；小心点，别被打倒了；看起来不太聪明的样子……正合适；站我后面，别送命
- 实现要点：
  - 随机池来源：遍历当前世界已加载实体（GLOBAL.Ents），筛选带 hostile/monster/epic 标签的单位，排除 player/companion/wall 等；作为跨模组的“全敌怪”随机集合
  - 召唤与复活：SpawnPrefab + follower:SetLeader；ListenForEvent("death") + inst:DoTaskInTime；random=true 时复活阶段重新抽取
  - Boss 削弱：components.health:SetMaxHealth(min(floor(maxhealth*0.5), 5000)) + health:SetPercent(1)；components.combat:SetDefaultDamage(defaultdamage*0.5)
- 设计理念：高娱乐的不确定性召唤，跨模组良好兼容，且对Boss做明显削弱以保持平衡


#### 巨兽召唤 (summon_boss)
- **效果**：召唤1只小型巨鹿协助战斗
- **费用**：120恩惠
- **持续时间**：60秒
- **特殊机制**：体型缩放至50%，血量和攻击力相应调整
- **实现**：生成deerclops，应用Transform:SetScale
- **设计理念**：顶级召唤物，短时间内的强大支援

### 6.5 农民系 (Farmer) - 农业生产强化

#### 绿拇指 (farmer_growth)
- **效果**：玩家周围8格范围内植物生长速度加快100%
- **费用**：30恩惠
- **实现**：每10秒扫描周围植物，动态调整growable组件的生长时间
- **适用对象**：所有农场作物、浆果丛、草丛、树木等有生长阶段的植物
- **技术细节**：减少现有生长任务的剩余时间，实现真正的生长加速
- **设计理念**：基础农业强化，提升农场效率，适合建设型玩家

#### 自动收获 (farmer_harvest)
- **效果**：玩家周围1格范围内成熟作物自动收获
- **费用**：55恩惠
- **实现**：每5秒扫描pickable物品，自动执行采集动作
- **智能机制**：优先放入背包，背包满时掉落在地上
- **事件触发**：自动触发picksomething事件，与丰收祝福联动
- **平衡设计**：1格范围需要玩家精确定位，避免过度自动化
- **设计理念**：解放双手的同时保持操作性，提供便利但不失控制感的游戏体验

#### 丰收祝福 (farmer_blessing)
- **效果**：采集时恢复饥饿+10、理智+5、生命+3
- **费用**：75恩惠
- **实现**：监听picksomething事件，每次采集都恢复三维属性
- **联动效果**：与自动收获完美配合，每次自动采集都触发恢复
- **视觉反馈**：显示"丰收祝福!"提示，增强用户体验
- **设计理念**：采集型玩法的综合强化，体现农民的生活智慧和自给自足

### 6.6 系统机制

#### 解锁系统
- 使用Favor代币购买被动恩惠
- 费用设计：
  - **战士系**：25-50恩惠（基础物理强化）
  - **法师系**：40-80恩惠（技能型玩法，操作要求高）
  - **召唤师系**：45-120恩惠（策略型玩法，效果最强）
  - **农民系**：30-75恩惠（生产型玩法，专注资源获取）
- 体现稀有度和强度的递增关系

#### 装备系统
- 最多同时装备2个被动恩惠
- 支持跨职业混搭，创造独特的build组合
- 可随时更换装备，适应不同场景和玩法需求
- **推荐组合**：
  - 战士+法师：近战输出+技能增强
  - 召唤师+农民：生物协战+资源支援
  - 农民+农民：专业农业发展路线

#### 数据持久化
- 完整的存档保存和加载
- 重生后自动重新应用效果
- 兼容旧版本存档

#### 用户界面
- 按职业分类显示被动恩惠
- 清晰的状态标识：[已装备]、[已解锁]、[需要X恩惠]
- 完整的使用说明和命令指南

### 6.7 平衡性设计

#### 费用平衡
- **战士系**：基础物理强化，费用相对较低，适合新手
- **法师系**：技能型玩法，费用中等，需要操作技巧和策略思考
- **召唤师系**：策略型玩法，费用最高，但提供强大的战斗支援
- **农民系**：生产型玩法，费用中等，专注于资源获取和生存支援

#### 效果强度
- 所有效果都有明显的游戏影响，但不破坏核心平衡
- 高费用被动恩惠提供更强的效果和更高的便利性
- **数值设计**：
  - 移速加成：20%（明显提升但不过强）
  - 攻击力加成：30%（显著战斗强化）
  - 连击系统：最多200%伤害（需要技巧）
  - 植物生长：100%加速（明显效果）
  - 自动收获：1格范围（便利但需要精确定位）
  - 三维恢复：饥饿+10、理智+5、生命+3（适中恢复）
- **平衡调整**：自动收获范围从6格调整为1格，避免过度自动化
- 避免过于强大导致游戏失衡

#### 职业特色
- 每个职业都有独特的游戏体验和发展路径
- 鼓励玩家尝试不同的职业组合，创造个性化build
- 支持多样化的游戏风格，满足不同类型玩家需求
- **职业定位**：
  - **战士系**：适合喜欢直接战斗的玩家
  - **法师系**：适合喜欢技能操作和策略的玩家
  - **召唤师系**：适合喜欢策略玩法和生物协战的玩家
  - **农民系**：适合喜欢建设、采集和和平发展的玩家

## 七、MVP 实施路线（进展更新）
1) 框架与组件 ✅
   - 搭建 modmain.lua、modinfo.lua、scripts/components/{modworld_global.lua, modplayer_boons.lua, modplayer_rep.lua, modplayer_reroll.lua}
   - 世界组件改为 AddPrefabPostInit("world") 挂载，保证 OnLoad 时序正确
   - 统一使用 ms_nextcycle 作为“新一天”触发
2) UI系统 ✅（第一版美化）
   - 创建 CaravanScreen 界面，标签页移到底部；移除底部“刷新/关闭”栏
   - V键快捷键 + cui命令打开UI（聊天状态不响应 V）
   - 词条页提供“重掷词条”按钮并与玩家每日次数联动
3) 每日词条 v1 ✅（持久化 + 每人每日一次重掷 + 真实效果）
   - 世界保存 mutators 与 current_day，读档后不变；新一天自动刷新
   - 玩家保存 last_reroll_day 与 reroll_used_today
   - 实现真实游戏效果：正向（丰收+1、疾风步伐、铁胃、澄澈心智、夜视）、负向（脆弱工具、不眠之夜、笨拙之手、负重前行）、中性（玻璃大炮）
4) 技术修复 ✅（GLOBAL变量访问问题）
   - 修复 DoTaskInTime 调用错误：组件中使用 self.inst:DoTaskInTime() 而非 self:DoTaskInTime()
   - 修复 GLOBAL 变量访问：在所有系统文件和组件文件中添加 local GLOBAL = rawget(_G, "GLOBAL") or _G
   - 涉及文件：mutator_effects.lua, commands.lua, modworld_global.lua
   - 解决 strict 模式下的变量声明错误，确保 AllPlayers 等全局变量正常访问
5) 合约 v1：2–3 种任务类型与事件计数 ✅
   - 实现击杀合约（监听killed事件）
   - 实现建设合约（监听onbuilt事件）
   - 实现交付合约（cdeliver命令）
   - 合约自动生成和替换系统
   - 奖励发放（恩惠）
   - UI界面显示合约详情
6) Favor 与被动 v1：职业被动恩惠系统 ✅ (重新设计 + 农民系扩展)
   - **职业系统**：战士、法师、召唤师、农民四大方向，总共12种被动恩惠
   - **战士系**：战士疾行(移速+20%)、战士之力(攻击+30%)、连击专精(最多5层200%伤害)
   - **法师系**：毒素掌控(DOT毒伤5秒)、蓄力打击(最多300%伤害)、闪现术(8格传送+cteleport命令)
   - **召唤师系**：蜘蛛召唤(2只120秒)、猪人护卫(1只180秒)、巨兽召唤(小巨鹿60秒)
   - **农民系**：绿拇指(8格范围植物生长+100%)、自动收获(1格范围自动采集)、丰收祝福(采集恢复三维)
   - **解锁系统**：使用Favor代币购买，费用25-120恩惠，四个职业费用递增合理
   - **装备系统**：最多同时装备2个被动恩惠，支持跨职业混搭创造独特build
   - **技术实现**：基于官方API的正确实现，修复了所有已知问题，包含复杂游戏机制
   - **命令系统**：cboon list/unlock/equip/unequip + cteleport传送命令，完整交互体验
   - **UI界面**：按四大职业分类显示，清晰的状态标识和完整的使用说明
   - **数据持久化**：完整的存档支持，重生后自动重新应用所有效果
   - **详细设计**：参见第六章职业被动恩惠系统详细设计
7) 献祭轮盘 v1：四种献祭类型与极端风险奖励系统（待实现）
8) 商队 v1（猪商队）：固定交易表与刷新机制（待实现）
9) Boss 进化 v1：巨鹿、海象各1个行为变体（待实现）
10) 配置开关与数值整理，性能与兼容性检查

## 八、测试计划
### 8.1 基础功能测试
- 本地主机开图，Day 1 验证词条生成与 /reroll；Day 2 刷新新词条
- 刷新/完成合约计数正确，Favor 结算与被动效果生效
- **职业被动恩惠系统测试**：
  - 战士系：验证移速加成20%、攻击力提升30%、连击系统(连续攻击同一目标最多5层)
  - 法师系：验证毒伤DOT效果(每秒10点持续5秒)、蓄力攻击机制(最多300%)、cteleport传送功能(8格距离30秒冷却)
  - 召唤师系：验证蜘蛛召唤(2只120秒)、猪人护卫(1只180秒)、巨兽召唤(小巨鹿60秒)，友好设置，跟随机制
  - 农民系：验证绿拇指(8格范围植物生长加速100%)、自动收获(1格范围每5秒)、丰收祝福(采集恢复饥饿+10理智+5生命+3)
  - 装备系统：验证最多2个被动恩惠，跨职业混搭，装备/卸下功能，推荐组合测试
  - UI界面：验证四大职业分类显示，状态标识，完整的命令说明和ID列表
- **献祭轮盘系统测试**：
  - 血祭轮盘：验证50%生命值消耗，死亡风险机制，奖励正确发放
  - 魂祭轮盘：验证80理智消耗，传说奖励5%概率，效果正确应用
  - 饥祭轮盘：验证饥饿值清零，高概率大奖机制
  - 物祭轮盘：验证物品稀有度识别，不同稀有度概率调整正确
  - 失败惩罚：验证30%额外惩罚概率，永久上限减少机制，诅咒效果应用
  - 上限保护：验证最低上限阈值保护，防止属性过低
  - 永久效果：验证属性上限提升/减少的持久化保存
  - 物品识别：验证稀有度数据库，跨模组物品识别，标签检测
  - UI界面：验证献祭状态显示，风险警告，物品稀有度显示，历史记录
  - 命令系统：验证/sacrifice查看、/spin献祭功能，安全确认机制，稀有度查询
- 商队按周期出现并能交易
- 刷 Boss 观察变体是否触发，掉落是否附加
- 存档/读档数据保持一致，被动恩惠效果正确恢复，献祭统计和永久奖励数据保存

### 8.2 技术修复验证 ✅
- **GLOBAL变量访问测试**：使用 `dofile("mods/thinking/test_global_fix.lua")` 验证所有系统文件正常加载
- **组件方法调用测试**：确认世界组件中的DoTaskInTime正常工作，无运行时错误
- **词条效果应用测试**：验证MutatorEffects系统能正确访问AllPlayers等全局变量
- **UI界面测试**：确认V键和cui命令能正常打开界面，无GLOBAL相关错误
- **聊天命令测试**：验证commands.lua中的聊天命令系统正常工作

## 九、技术实现细节与修复记录
### 9.1 GLOBAL变量访问修复
**问题**：在饥荒DST的strict模式下，通过require加载的文件无法直接访问GLOBAL变量，导致运行时错误。

**解决方案**：在所有需要访问GLOBAL变量的文件开头添加：
```lua
-- 确保能访问全局变量
local GLOBAL = rawget(_G, "GLOBAL") or _G
```

**涉及文件**：
- `scripts/systems/mutator_effects.lua`
- `scripts/systems/commands.lua`
- `scripts/components/modworld_global.lua`

### 8.2 组件方法调用修复
**问题**：在组件中错误使用 `self:DoTaskInTime()`，但DoTaskInTime是实体方法，不是组件方法。

**解决方案**：使用 `self.inst:DoTaskInTime()` 来调用实体的定时任务方法。

**修复位置**：`modworld_global.lua` 第38行

### 8.3 环境兼容性
- 使用 `rawget(_G, "GLOBAL") or _G` 确保在不同mod环境中都能正常工作
- 避免直接访问未声明的全局变量，符合DST的strict模式要求
- 所有系统文件都采用统一的GLOBAL访问模式

## 十、兼容性与风险
- 钩子尽量使用 AddPrefabPostInit / AddComponentPostInit；少量行为微调通过组件参数
- 所有系统提供倍率/开关，默认中度
- 避免全局覆写；尽量事件驱动（ListenForEvent）
- 已解决GLOBAL变量访问和组件方法调用的兼容性问题

## 十一、后续扩展
- 主题日、更多阵营与商队、任务宾果、更多 Boss 进化分支

---
本蓝图为实施依据，接下来将初始化代码骨架（无资源），按“六、MVP 实施路线”逐步实现与测试。

## 十二、修复日志
### 2024年修复记录
- **GLOBAL变量访问修复**：解决了systems目录和components目录下文件无法访问GLOBAL变量的问题
- **组件方法调用修复**：修正了组件中错误调用DoTaskInTime的问题
- **环境兼容性提升**：确保mod在不同DST环境中都能正常运行
- **测试框架完善**：添加了完整的技术修复验证测试

### 2024年功能更新记录
- **职业被动恩惠系统重新设计**：按照战士、法师、召唤师、农民四个职业方向完全重新实现
  - **战士系**：战士疾行(移速+20%)、战士之力(攻击+30%)、连击专精(最多5层200%伤害)
  - **法师系**：毒素掌控(DOT毒伤)、蓄力打击(最多300%伤害)、闪现术(cteleport命令)
  - **召唤师系**：蜘蛛召唤(2只)、猪人护卫(1只)、巨兽召唤(小巨鹿)
  - **农民系**：绿拇指(植物生长+100%)、自动收获(1格范围)、丰收祝福(采集恢复三维)
  - **技术实现**：基于官方API的正确实现，修复了SourceModifierList参数顺序等关键问题
  - **用户体验**：四大职业分类UI、丰富的视觉反馈、完整的命令系统和帮助信息
  - **数据持久化**：完整的存档支持，重生后自动重新应用所有效果
  - **系统完整性**：12种被动恩惠，覆盖战斗、技能、策略、建设四大游戏方向

- **农民系平衡性调整**：自动收获范围从6格调整为1格
  - **调整原因**：6格范围过于强大，可能破坏游戏平衡
  - **新设计**：1格范围需要玩家精确定位，保持便利性的同时增加操作性
  - **用户体验**：避免过度自动化，保持游戏的挑战性和互动性
  - **技术实现**：简单的参数调整，保持系统稳定性



## 十三、献祭轮盘系统详细设计

### 13.1 系统概述
献祭轮盘是一个极端风险的赌博系统，玩家需要付出生命值、理智值、饥饿值或珍贵物品作为代价。这个系统体现了"高风险高回报"的核心理念，每次献祭都可能带来巨大收益或严重后果。

### 13.2 献祭类型与代价设计

#### 血祭轮盘（Blood Sacrifice）
- **代价**：消耗50%当前生命值（最少20点，不足20点无法使用）
- **死亡风险**：如果当前生命值≤40，有25%概率直接死亡
- **概率分布**：
  - 小奖励（60%）：C级资源抽取、即时生命恢复
  - 中等奖励（25%）：B级资源抽取、Favor 20-40点
  - 大奖（12%）：永久+10生命上限、A级资源抽取
  - 超级大奖（3%）：永久+25生命上限、临时攻击吸血能力

#### 魂祭轮盘（Soul Sacrifice）
- **代价**：消耗80点理智值（不足80点无法使用）
- **疯狂风险**：理智降至0以下时触发持续疯狂状态
- **概率分布**：
  - 中等奖励（50%）：即时理智恢复、临时强力buff
  - 大奖（30%）：永久+15理智上限、A级资源抽取
  - 超级大奖（15%）：永久+30理智上限、临时夜视能力
  - 传说奖励（5%）：永久+50理智上限、S级资源抽取

#### 饥祭轮盘（Hunger Sacrifice）
- **代价**：消耗全部饥饿值（降至0）
- **饥饿风险**：立即进入饥饿状态，持续掉血直到进食
- **概率分布**：
  - 中等奖励（40%）：B级资源抽取（食物类）、临时消化buff
  - 大奖（35%）：永久+20饥饿上限、A级资源抽取
  - 超级大奖（20%）：永久+40饥饿上限、临时食物效果翻倍
  - 传说奖励（5%）：永久+75饥饿上限、S级资源抽取

#### 物祭轮盘（Item Sacrifice）
- **代价**：消耗物品，系统自动识别稀有度等级
- **动态稀有度识别系统**（基于玩家常用判断法）：
  - **C级（Common）**：常见、易合成/易采集
    - 可再生且获取简单：木材、石头、草、树枝、食物
    - 基础合成物：基础工具、简单装备
    - 判断标准：无限再生 + 低风险获取 + 无特殊条件
  - **B级（Uncommon）**：稳定可再生但需要准备与风险
    - 需要一定准备：蜘蛛丝（需战斗）、猪皮（需交易/击杀）
    - 中等风险获取：金块（需挖矿）、活木（需砍树精）
    - 判断标准：可再生 + 中等风险/准备 + 一般条件
  - **A级（Rare）**：季节或高门槛流程限定，可再生但效率低
    - 季节限定：海象牙（冬季）、鹿角（春季巨鹿）
    - 低概率掉落：齿轮（风滚草低概率）、宝石（企鹅低概率）
    - 高门槛流程：噩梦燃料（需理智低）、铥矿（需深入遗迹）
    - 判断标准：限定条件 + 低效率再生 + 高门槛
  - **S级（Legendary）**：一次性/不可再生/极低掉率/首领限定
    - Boss限定掉落：巨鹿眼球、蜂王果冻、远古守护者掉落
    - 一次性物品：远古蓝图、特殊建筑蓝图
    - 极低掉率：某些稀有蓝图、事件限定物品
    - 判断标准：不可再生 OR 极低掉率 OR Boss限定
- **概率分布**：
  - **C级**：60%小奖励，30%中等奖励，10%大奖
  - **B级**：40%中等奖励，35%大奖，20%超级大奖，5%传说奖励
  - **A级**：30%大奖，40%超级大奖，25%传说奖励，5%神话奖励
  - **S级**：20%超级大奖，50%传说奖励，30%神话奖励
- **失败风险**：稀有度越高，失败惩罚越严重

### 13.3 失败惩罚机制（10%概率）
所有献祭都有10%概率触发额外惩罚，从以下10种惩罚中随机选择1种：

#### 惩罚类型详细说明
1. **永久上限减少**：
   - 血祭：生命上限-5（最低50）
   - 魂祭：理智上限-10（最低50）
   - 饥祭：饥饿上限-15（最低100）
   - 物祭：随机属性上限-5到-15

2. **背包物品掉落**：
   - 背包中所有物品（除装备外）全部掉落到地面
   - 掉落物品散布在玩家周围3格范围内
   - 装备栏物品（帽子、身体、手持）不受影响

3. **体温异常**：
   - 随机触发过热或过冷状态
   - 持续10秒，无法通过装备或建筑缓解
   - 过热：持续掉血，移速-20%
   - 过冷：持续掉理智，攻击-20%

4. **敌对生物召唤**：
   - 猎犬群：2-3只红/蓝猎犬，持续追击60秒
   - 蜘蛛群：3-4只蜘蛛战士，在附近生成
   - 暗影生物：1-2只暗影触手（魂祭特有）

5. **工具损坏**：
   - 随机选择背包中1-2件工具
   - 耐久度直接归零（完全损坏）
   - 优先选择耐久度较高的工具

6. **负面光环**：
   - 移速-30%，攻击力-20%
   - 持续120秒，无法通过任何方式移除
   - 影响范围：仅玩家自身

7. **理智崩溃**：
   - 理智值强制降至0
   - 进入持续疯狂状态60秒
   - 期间会出现暗影生物幻象攻击

8. **饥饿诅咒**：
   - 食物效果减少75%
   - 持续2个游戏日
   - 影响所有食物的饥饿、生命、理智恢复

9. **黑暗诅咒**：
   - 失去夜视能力
   - 所有光源效果减少50%
   - 持续1个游戏日

10. **随机传送**：
    - 立即传送到地图随机位置
    - 可能传送到危险区域（沼泽、蜘蛛巢穴附近等）
    - 传送后有3秒无敌时间防止立即死亡

### 13.4 诅咒与救赎系统
- **诅咒效果**：失败后可能获得持续性负面效果
- **救赎机制**：
  - 完成特定任务可解除诅咒
  - 使用稀有物品（如生命护符）可立即解除
  - 时间自然消退（1-3天不等）
- **诅咒叠加**：多次失败可能叠加诅咒效果，风险递增

### 13.5 动态稀有度识别技术实现
- **多维度判断算法**：
  ```lua
  function GetItemRarity(item)
      local rarity_score = 0
      local prefab = item.prefab

      -- 1. 基础稀有度数据库（核心物品）
      local base_rarity = KNOWN_ITEM_RARITY[prefab] or 0
      rarity_score = rarity_score + base_rarity

      -- 2. 标签检测（跨模组兼容）
      if item:HasTag("boss_drop") then rarity_score = rarity_score + 30 end
      if item:HasTag("ancient") then rarity_score = rarity_score + 25 end
      if item:HasTag("magic") then rarity_score = rarity_score + 20 end
      if item:HasTag("rare") then rarity_score = rarity_score + 15 end
      if item:HasTag("seasonal") then rarity_score = rarity_score + 15 end
      if item:HasTag("blueprint") then rarity_score = rarity_score + 10 end

      -- 3. 组件检测
      if item.components.spellcaster then rarity_score = rarity_score + 20 end
      if item.components.armor and item.components.armor.maxcondition > 1000 then
          rarity_score = rarity_score + 15
      end
      if item.components.weapon and item.components.weapon.damage > 50 then
          rarity_score = rarity_score + 10
      end

      -- 4. 名称启发式（模组物品）
      local name_lower = string.lower(prefab)
      if string.find(name_lower, "boss") or string.find(name_lower, "epic") then
          rarity_score = rarity_score + 25
      end
      if string.find(name_lower, "rare") or string.find(name_lower, "legendary") then
          rarity_score = rarity_score + 20
      end
      if string.find(name_lower, "ancient") or string.find(name_lower, "ruin") then
          rarity_score = rarity_score + 15
      end

      -- 5. 分数转换为等级
      if rarity_score >= 40 then return "S" end
      if rarity_score >= 25 then return "A" end
      if rarity_score >= 10 then return "B" end
      return "C"
  end
  ```

- **核心物品数据库**（基于玩家判断法）：
  ```lua
  KNOWN_ITEM_RARITY = {
      -- S级：Boss限定/一次性/极低掉率
      ["deerclops_eyeball"] = 50,    -- Boss限定
      ["walrus_tusk"] = 45,          -- 季节Boss限定
      ["ancient_key"] = 50,          -- 一次性
      ["shadow_atrium"] = 50,        -- 极稀有
      ["royal_jelly"] = 45,          -- Boss限定

      -- A级：季节/高门槛/低效率再生
      ["gears"] = 30,                -- 低概率再生
      ["nightmarefuel"] = 35,        -- 高门槛流程
      ["thulecite"] = 40,            -- 遗迹限定
      ["redgem"] = 25,               -- 低概率掉落
      ["bluegem"] = 25,              -- 低概率掉落
      ["purplegem"] = 30,            -- 更低概率

      -- B级：需准备风险但可再生
      ["livinglog"] = 15,            -- 需战斗树精
      ["silk"] = 12,                 -- 需战斗蜘蛛
      ["pigskin"] = 15,              -- 需击杀/交易
      ["goldnugget"] = 10,           -- 需挖矿

      -- C级：常见易得
      ["log"] = 0, ["rocks"] = 0, ["cutgrass"] = 0,
      ["twigs"] = 0, ["flint"] = 0, ["berries"] = 0
  }
  ```

- **跨模组兼容性**：
  - 未知物品默认C级，通过标签和组件自动提升
  - 模组作者可通过标签标记稀有度
  - 支持动态学习：记录玩家献祭结果，调整未知物品稀有度

### 13.6 技术实现要点
- **安全检查**：献祭前检查玩家当前状态，防止意外死亡
- **确认机制**：高风险献祭需要二次确认，显示死亡风险警告和可能的上限减少
- **属性修改**：通过官方API安全修改玩家三维属性和上限
- **上限保护**：设置最低上限阈值，防止玩家属性过低无法游戏
- **UI界面**：献祭页面显示当前三维、风险警告、物品稀有度、历史统计
- **命令系统**：
  - `/sacrifice`：查看献祭状态和统计信息
  - `/spin blood/soul/hunger/item`：进行对应类型的献祭
  - `/sacrifice confirm`：确认高风险献祭
  - `/curse status`：查看当前诅咒状态
  - `/rarity`：查看手持物品的稀有度和判断依据
  - `/rarity scan`：扫描背包所有物品的稀有度
  - `/rarity debug <物品名>`：显示稀有度计算详情（管理员）
- **数据持久化**：永久属性加成/减少、诅咒状态、统计数据完整保存
- **网络同步**：使用RPC确保多人游戏中的同步

### 13.7 极端风险体验设计
- **真正的代价**：不是虚拟货币，而是玩家的生命、理智、饥饿值
- **永久性后果**：失败不仅损失当前资源，还会永久减少属性上限
- **不可逆转**：上限减少无法通过普通手段恢复，只能通过更高风险的献祭来补偿
- **心理博弈**：
  - 诱惑：永久属性提升的巨大收益
  - 恐惧：可能永久削弱角色的风险
  - 绝望：连续失败导致角色越来越弱
  - 疯狂：为了挽回损失而进行更危险的献祭
- **风险螺旋**：失败后为了恢复实力，玩家往往会进行更高风险的献祭
- **社交压力**：
  - 成功时的全服公告带来荣耀感
  - 失败时的属性减少带来羞耻感
  - 其他玩家的成功会刺激更多冒险
- **策略深度**：
  - 什么时候献祭（满血时血祭更安全）
  - 献祭什么（物品稀有度影响收益）
  - 如何止损（连续失败时是否继续）
  - 稀有度判断（学会识别真正有价值的物品）
- **稀有度系统的策略价值**：
  - **收集策略**：玩家会主动寻找和囤积高稀有度物品
  - **风险评估**：S级物品献祭风险极高，但回报也最大
  - **模组探索**：鼓励玩家尝试其他模组，寻找稀有物品
  - **知识积累**：玩家会学习游戏机制，了解物品获取难度
- **成瘾设计**：
  - 间歇性强化：不定期的大奖维持参与动机
  - 损失厌恶：已经投入的代价让玩家难以停止
  - 社会比较：与其他玩家的属性对比产生竞争欲望
  - 收集欲望：寻找和识别稀有物品的成就感
  - 知识炫耀：了解稀有度系统的玩家获得优越感

## 十四、合约系统 v2 可靠实现清单与规范（最终版）

本节为“远征合约（Contracts）”的完整可靠性实现清单，结合已确认产品策略：
- 击杀归属：召唤物代杀计入；陷阱与环境致死不计入
- 交付限制：保留耐久/新鲜度限制，默认阈值由我们提供，且可在配置中调整
- 刷新机制：每日刷新新合约；当日完成的不替换，只显示为“已完成”直至次日
- 难度分层：需要（用于公平与可完成性）；不使用黑名单（跨模组目标自动纳入）

### 13.1 网络与分片（Shards）架构（最终）
- 服务器权威：Forest 分片（或主世界 mastersim）为“合约权威源”
- 统一使用标准 Mod RPC，不使用自定义 TheShard RPC
- 状态分发：使用一个小型 netvar（contracts_version）在 TheWorld 上广播“有更新”，客户端监听 dirty 后主动通过 RPC 拉取快照（JSON），而不是把大数据放进 netvars
- 客户端交互：UI 的“交付物品”“刷新/详情/重掷词条”等操作一律通过 RPC；客户端不直接修改服务端组件
- 失败安全：短时间窗去重（击杀/采集/制作各自窗口），断线后可再次请求快照恢复
- 现有 RPC 列表：
  - 合约：contract_request/contract_receive、contract_deliver
  - 词条：mutators_request/mutators_receive、mutators_reroll
  - 商队：caravan_request/caravan_receive
  - 刷新：contracts_refresh（全量；内测）、contract_refresh_one（单个；内测，5s 冷却）

### 13.1.1 客户端/服务端职责边界（现状）
- 客户端：
  - 仅显示与发起 RPC；所有阈值读 TUNING；不直接访问 TheWorld.components（除非处于服务端路径）
  - UI 缓存：contracts/mutators/caravan 快照保存在界面对象上；收到 *_receive 后刷新局部
- 服务端：
  - 维护合约/词条/商队状态与持久化；处理 RPC；通过 contracts_version 通知客户端拉取
- 标准写法：
  - 客户端 RPC：SendModRPCToServer(GetModRPC("thinking","..."))
  - 服务端回发：SendModRPCToClient(GetClientModRPC("thinking","..."), userid, payload)


### 13.2 目标池（跨模组动态纳入）
- 击杀目标池（动态）：
  - 构建方式：从 Prefabs 表枚举“可战斗”单位；当前实现使用名称启发式（spider/hound/...），并计划逐步引入标签驱动（monster/hostile）与白名单过滤
  - 季节/存在性过滤：按季节/世界状态清理不可达目标；避免抽取罕见或当下不可获得的单位
  - 权重：考虑出现频率/生态位/危险度；用于难度分层（Easy/Normal/Hard）
- 采集目标池（动态）：
  - 构建方式：枚举 pickable/harvestable 等特征；当前池可能较大（数百量级），后续将增加上限抽样与权重 TopN 以收敛
  - 可达性校验：需可在当前世界通过“采集/拾取/收获/破坏”获得；罕见/事件专属降权或剔除
- 制作/建造目标池（动态）：
  - 构建方式：从 AllRecipes 收集所有配方（包含建筑与制作）
  - 科技校验：仅纳入当前科技层可达或当日上限范围内配方（避免早期抽到不可做的物品）
- 不使用黑名单：默认纳入第三方模组内容；通过“最低可达性/稀有度阈值+抽样上限”自动过滤不合理目标

### 13.3 事件归属与计数（全覆盖）
- 击杀计数：
  - 事件源：优先玩家 "killed"；补充受害者死亡回调，读取 lastattacker/combattarget
  - 归属规则：
    - 若 lastattacker 是玩家 → 计入该玩家
    - 若 lastattacker 是玩家的召唤/随从（follower 且 leader 拥有 player 标签）→ 计入该 leader 玩家（召唤物代杀计入）
    - 若为陷阱/环境（火焰、寒冷、掉落、踩陷阱等）→ 不计入
  - 去重：对同一受害者 GUID 在短时间窗内仅计一次
- 采集计数（统一在“背包增加有效物品”时计数）：
  - 技术路径：对 Inventory/AddItem/GiveItem/ReceiveItem 注入服务端拦截（AddComponentPostInit），
    当“来源为采集/拾取/收获/破坏采得”时发出标准化事件 contract_item_added，按合约池匹配并累计数量
  - 覆盖场景：picksomething、拾取地面、挖掘/砍伐/采矿导致的掉落收集、收获型容器（蜂箱、农作物）
  - 去重与来源识别：读取 item 组件的来源标记（若缺省则以拾取动作代理判定），不计“脚本生成/交易/掉落再拾回作弊”
- 制作计数：
  - 技术路径：对 Builder 组件注入（AddComponentPostInit("builder")），在服务端拦截“制作完成”事件（成功扣材并生成成品后）统一计数
  - 覆盖：makeitem/builditem/buildstructure 等路径归一

### 13.4 刷新与生命周期（现状）
- 每日刷新：在 cycleschanged（新一天）时由权威分片重建全量合约列表（按难度配比与目标池生成）
- 当日完成：标记 completed=true；默认不即时替换（UI显示“已完成”）；目前实现中保留了一个替换函数 ReplaceCompletedContract 供调试/内测使用，但默认流程并不会自动替换
- 次日重置：完成与未完成均被新日列表替换（不继承进度）；已领取奖励不重复
- 内测刷新：
  - 全量刷新：contracts_refresh（无次数限制，仅内测开）
  - 单个刷新：contract_refresh_one（每合约索引 5s 冷却）
  - 行为：刷新后触发 contracts_version 脏标，客户端拉取快照刷新 UI

### 13.5 交付限制与配置
- 默认阈值（可配置，经 modmain 解析进 TUNING.CARAVAN）：
  - DELIVER_MIN_DURABILITY = 0.80（耐久≥80%）
  - DELIVER_MIN_FRESHNESS = 0.50（新鲜≥50%）
- 服务器权威验证：交付 RPC 在服务端校验阈值；客户端仅用于展示
- UI 显示：按钮 tooltip 展示“所需/有效/总计/不合格原因”；客户端从 TUNING 读取阈值
- 配置加载规范：运行期模块不直接调用 GetModConfigData；只读 TUNING（避免 modutil 的断言）

### 13.6 难度分层与权重
- 难度档：Easy/Normal/Hard（可配置占比，默认 40%/40%/20%）；配置若不严格等于1.0，总和将自动归一化
- 权重因素：
  - 可达性/季节性（当前日/季是否常见）
  - 玩家规模（AllPlayers 数量影响目标量级）
  - 世界进度（天数 cycles 越大，Hard 权重略升）
- 生成规则：
  - 同时在线玩家>n 时提高集体任务量（如击杀/采集/制作目标）
  - 禁止抽取“当前绝无可能完成”的目标（可达性检查失败即剔除）
- 校验与自愈：
  - 合约生成后进行有效性校验（放宽为“最小字段校验”，兼容动态目标池）
  - 若数量不足，立即补齐；避免“公告后大规模剔除”的体验

### 13.7 持久化与一致性
- OnSave/OnLoad：保存合约列表（id/type/target/goal/progress/completed/day_stamp/difficulty），读档后在同一日保持不变
- 自愈策略：检测到目标在当前环境不可达（卸载模组、季节变化导致完全不可得）时：
  - 当日：标注为“无效目标”，保持显示但不可完成（避免刷新穿模）；
  - 次日：自动替换为新目标

### 13.8 UI/UX 与客户端体验
- 数据来源：全部使用 netvars/同步快照渲染；不直接访问 TheWorld.components
- 行为：
  - “交付物品”发送 RPC 并在完成后延时小幅刷新
  - “查看详情”弹窗（或 talker + 控制台打印）显示来源说明与计数规则
- 失败提示：
  - 客户端离线/分片未同步 → 显示“同步中/请稍后”
  - 物品不合格 → 显示不合格明细（耐久/新鲜）

### 13.9 安全与性能
- 服务器校验一切来自客户端的请求；客户端仅做提示
- 事件拦截与枚举加缓存：
  - 目标池结果按日缓存
  - 物品来源识别采用“轻量标签+有限窗口”策略，避免频繁深度追踪
- 预算：
  - 目标池生成 ≤ 50ms；
  - 分片同步每次消息 ≤ 2KB；
  - 每日刷新时最多生成 3–5 个合约

### 13.10 测试与验收
- 单机/多人（同分片/跨分片）
- 模组增删热更后目标池应正确变化并不过度抽稀
- 召唤物代杀正确归属；陷阱/环境不计
- 采集/制作各来源路径均能计数；背包作弊路径不计
- 存档/读档、换日刷新、当天完成不替换逻辑正确
- UI 客户端可用（非主机也能查看与交互），交付 RPC 有效
- 验收标准：满足“对照要求核查”章节的所有标准与本节预算
