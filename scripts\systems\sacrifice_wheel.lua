-- 献祭轮盘服务端规则
local GLOBAL = rawget(_G, "GLOBAL") or _G
local TUNING = GLOBAL.TUNING or {}

local Rarity = require("systems/rarity")

local W = {}

local function Sac() return (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.SACRIFICE) or {} end

local function Now() return GLOBAL.GetTime() end
-- local function CurDay() return (GLOBAL.TheWorld and GLOBAL.TheWorld.state and GLOBAL.TheWorld.state.cycles) or 0 end

-- local function ClampLower(kind, value)
--     local lb = Sac().PENALTY and Sac().PENALTY.LOWER_BOUNDS or {health=50,sanity=50,hunger=100}
--     return math.max(lb[kind] or 0, value)
-- end

local function ApplyUpperDelta(player, kind, delta)
    if not player or not player.components or not player.components.modplayer_sacrifice then return end
    player.components.modplayer_sacrifice:Add<PERSON>ermanent<PERSON>elta(kind, delta)
end

local function ApplyCurse(player, id, days)
    if not player or not player.components or not player.components.modplayer_sacrifice then return end
    player.components.modplayer_sacrifice:AddCurse(id, days)
end

-- 资源池（仅原版/无需新贴图的资源）
local RESOURCE_POOLS = {
    C = { "log","rocks","cutgrass","twigs","flint","berries","ice","nitre","seeds" },
    B = { "silk","pigskin","stinger","spidergland","charcoal","goldnugget","beefalowool","honey" },
    A = { "gears","redgem","bluegem","purplegem","livinglog","nightmarefuel" },
    S = { "thulecite","walrus_tusk","deerclops_eyeball","royal_jelly" },
}

local GRADE_COEFF = { C=0.5, B=0.75, A=1.0, S=1.25 }
local TIER_RAND = {
    small = {1.0,1.5},
    mid   = {1.5,2.0},
    big   = {2.0,2.5},
    super = {2.5,3.0},
    legend= {3.0,3.5},
    myth  = {3.5,4.0},
}

local function RandomChoice(list)
    if not list or #list == 0 then return nil end
    return list[math.random(1,#list)]
end

local function SpawnStackToPlayer(player, prefab, amount)
    if amount <= 0 then return end
    local function give_stack(n)
        local ent = GLOBAL.SpawnPrefab(prefab)
        if not ent then return end
        if ent.components.stackable then
            local maxs = ent.components.stackable.maxsize or 20
            local sz = math.min(n, maxs)
            ent.components.stackable:SetStackSize(sz)
            n = n - sz
        end
        if player.components.inventory then
            player.components.inventory:GiveItem(ent)
        else
            ent.Transform:SetPosition(player.Transform:GetWorldPosition())
        end
        return n
    end
    local left = amount
    while left > 0 do
        local before = left
        left = give_stack(left) or 0
        if left == before then break end
    end
end

local function GiveResourceByGrade(player, tier, prefer_grade)
    -- 档位到稀有度映射
    local g = prefer_grade or (tier == "small" and "C") or (tier == "mid" and "B") or (tier == "big" and "A") or (tier == "super" and (math.random()<0.5 and "A" or "S")) or "S"
    local pool = RESOURCE_POOLS[g] or RESOURCE_POOLS.C

    -- 小/中/大奖：1-2种；其余：1种
    local kinds = (tier == "small" or tier == "mid" or tier == "big") and math.random(1,2) or 1
    for _=1,kinds do
        local prefab = RandomChoice(pool)
        if prefab then
            -- 计算数量：Base = 堆叠上限 × 系数；数量 = Base × Rand
            local dummy = GLOBAL.SpawnPrefab(prefab)
            local maxs = (dummy and dummy.components.stackable and dummy.components.stackable.maxsize) or 1
            if dummy then dummy:Remove() end
            local base = math.max(1, math.floor(maxs * (GRADE_COEFF[g] or 1.0)))
            local rmin,rmax = table.unpack(TIER_RAND[tier] or {1.0,1.5})
            local qty = math.floor(base * (rmin + (rmax-rmin)*math.random()))
            qty = math.max(1, qty)
            SpawnStackToPlayer(player, prefab, qty)
        end
    end
end

local function AllowedToDie()
    return Sac().ALLOW_DEATH ~= false
end

local function EnoughResource(player, t)
    if t == "blood" then
        if not player.components.health then return false end
        local hp = player.components.health.currenthealth or 0
        local maxh = player.components.health.maxhealth or 150
        local cost = math.max((Sac().COSTS.BLOOD_MIN_HP or 20), maxh * (Sac().COSTS.BLOOD_HP_PERCENT or 0.5))
        if AllowedToDie() then
            return hp > 0 -- 允许致死：只要当前有血就允许执行，结果可能死亡
        else
            return hp > cost -- 不允许致死则需要支付后仍存活
        end
    elseif t == "soul" then
        if not player.components.sanity then return false end
        local san = player.components.sanity.current or player.components.sanity.current_sanity or 0
        return san >= (Sac().COSTS.SOUL_SANITY or 80)
    elseif t == "hunger" then
        if not player.components.hunger then return false end
        local hg = player.components.hunger.current or 0
        return hg > 0
    elseif t == "item" then
        -- 在调用处检查具体物品
        return true
    end
    return false
end

local function ApplyCost(player, t, opt)
    if t == "blood" then
        local maxh = player.components.health.maxhealth or 150
        local cost = math.max((Sac().COSTS.BLOOD_MIN_HP or 20), maxh * (Sac().COSTS.BLOOD_HP_PERCENT or 0.5))
        player.components.health:DoDelta(-cost, false, "sacrifice")
        if player.components.health:IsDead() and not AllowedToDie() then
            -- 若不允许死亡，回滚（当前配置允许死亡）
        end
    elseif t == "soul" then
        player.components.sanity:DoDelta(-(Sac().COSTS.SOUL_SANITY or 80))
    elseif t == "hunger" then
        local cur = player.components.hunger.current or 0
        player.components.hunger:DoDelta(-cur)
    elseif t == "item" then
        if opt and opt.item then
            local item = opt.item
            local inv = player.components.inventory
            if inv then inv:RemoveItem(item, true):Remove() end
        end
    end
end

local function Roll(prob)
    return math.random() < prob
end

-- 返回奖档 key（集成幸运加成）
local function RollTier(player, t, grade)
    local odds = Sac().ODDS or {}
    local lb = 0
    if player and player.components and player.components.modplayer_sacrifice then
        lb = player.components.modplayer_sacrifice:GetLuckBoost() or 0
    end
    lb = math.max(0, math.min(lb, 0.5)) -- 上限保护，避免畸变

    if t == "item" then
        -- 基于稀有度偏移：C/B/A/S 逐步偏向高档
        local base = odds.BLOOD or {small=0.6,mid=0.25,big=0.12,super=0.03}
        local g = grade or "C"
        local shift = { C = {0,0,0,0}, B = {0,-0.05, 0.04, 0.01}, A = {-0.10, 0.05, 0.04, 0.01}, S = {-0.30, 0.10, 0.15, 0.05} }
        local s = shift[g] or shift.C
        local tiers = {
            small = math.max(0, (base.small or 0.6) + s[1]),
            mid   = math.max(0, (base.mid   or 0.25) + s[2]),
            big   = math.max(0, (base.big   or 0.12) + s[3]),
            super = math.max(0, (base.super or 0.03) + s[4]),
        }
        -- 幸运加成：提高大/超档权重，降低小/中档
        tiers.big   = tiers.big   * (1 + lb)
        tiers.super = tiers.super * (1 + lb * 1.5)
        tiers.small = tiers.small * (1 - lb * 0.5)
        tiers.mid   = tiers.mid   * (1 - lb * 0.25)
        if tiers.small < 0 then tiers.small = 0 end
        if tiers.mid   < 0 then tiers.mid   = 0 end

        local total = (tiers.small+tiers.mid+tiers.big+tiers.super)
        local r = math.random() * math.max(total, 0.0001)
        if r < tiers.small then return "small" end
        if r < tiers.small+tiers.mid then return "mid" end
        if r < tiers.small+tiers.mid+tiers.big then return "big" end
        return "super"
    else
        local base = (t == "blood" and odds.BLOOD) or (t == "soul" and odds.SOUL) or odds.HUNGER or {}
        -- 复制并应用幸运加成（若存在 legend/myth 则放大其权重）
        local adj = {}
        for k,v in pairs(base) do adj[k] = v end
        if lb > 0 then
            if adj.big   then adj.big   = adj.big   * (1 + lb) end
            if adj.super then adj.super = adj.super * (1 + lb*1.5) end
            if adj.legend then adj.legend = adj.legend * (1 + lb*2.0) end
            if adj.myth   then adj.myth   = adj.myth   * (1 + lb*3.0) end
            if adj.small then adj.small = math.max(0, adj.small * (1 - lb*0.5)) end
            if adj.mid   then adj.mid   = math.max(0, adj.mid   * (1 - lb*0.25)) end
        end
        local pool = {}
        for k,v in pairs(adj) do pool[#pool+1] = {k=k, v=v} end
        table.sort(pool, function(a,b) return a.v < b.v end)
        local acc = 0
        local r = math.random()
        for _, e in ipairs(pool) do
            acc = acc + e.v
            if r <= acc then return e.k end
        end
        return pool[#pool] and pool[#pool].k or "mid"
    end
end

local function CapKindForType(t)
    if t == "blood" then return "health" end
    if t == "soul"  then return "sanity" end
    if t == "hunger" then return "hunger" end
    local kinds = {"health","sanity","hunger"}
    return kinds[math.random(1,#kinds)]
end

-- 完整奖励池实现
local REWARD_POOLS = {
    small = {
        -- 资源类 (1-23)
        {type="resource", weight=23},
        -- 星尘 (24-25)
        {type="favor", amount=10, weight=1},
        {type="favor", amount=15, weight=1},
        -- 临时buff (26-50)
        {type="temp_buff", effect="speed", value=0.1, duration=60, weight=25}
    },
    mid = {
        -- 资源类 (1-11)
        {type="resource", weight=11},
        -- 星尘 (12-13)
        {type="favor", amount=30, weight=1},
        {type="favor", amount=40, weight=1},
        -- 临时buff/恢复 (14-30)
        {type="temp_buff", effect="speed", value=0.2, duration=120, weight=17}
    },
    big = {
        -- 资源类 (1-3)
        {type="resource", weight=3},
        -- 星尘 (4-5)
        {type="favor", amount=120, weight=1},
        {type="favor", amount=160, weight=1},
        -- 永久上限 (6-8)
        {type="permanent_cap", amount=5, weight=3},
        -- 蓝图/光环/强化buff (9-20)
        {type="blueprint", tier="advanced", weight=1},
        {type="temp_aura", effect="team_speed", value=0.1, duration=120, range=6, weight=1},
        {type="temp_buff", effect="speed", value=0.3, duration=180, weight=10}
    },
    super = {
        -- 资源类 (1)
        {type="resource", weight=1},
        -- 永久上限 (2-4)
        {type="permanent_cap", amount=20, weight=3},
        -- 召唤 (5-6)
        {type="summon", creature="hound", count=2, duration=90, friendly=true, weight=2},
        -- 强力能力 (7-10)
        {type="temp_buff", effect="all_regen", value=1.0, duration=300, weight=4}
    },
    legend = {
        -- 资源类 (1)
        {type="resource", weight=1},
        -- 永久上限 (2-4)
        {type="permanent_cap", amount=50, weight=3},
        -- 超强护佑 (5)
        {type="temp_protection", effects={"vision","nightvision","waterproof","insulation"}, duration=300, weight=1}
    },
    myth = {
        -- 终极特权 (1)
        {type="luck_boost", value=0.2, duration=20*GLOBAL.TUNING.TOTAL_DAY_TIME, weight=1}, -- 1个季节
        -- 终极强化 (2)
        {type="ultimate_boost", caps={health=50,sanity=50,hunger=50}, title="幸运之神", weight=1},
        -- 神话领域 (3)
        {type="myth_aura", effects={"shield","speed","efficiency"}, duration=180, range=8, weight=1}
    }
}

local function ApplySpecificReward(player, reward, prefer_grade)
    local talker = player.components.talker

    if reward.type == "resource" then
        GiveResourceByGrade(player, "small", prefer_grade) -- 档位在外层决定
    elseif reward.type == "favor" then
        local b = player.components.modplayer_boons
        if b then b:AddFavor(reward.amount or 10) end
        if talker then talker:Say(string.format("获得星尘 +%d", reward.amount or 10)) end
    elseif reward.type == "permanent_cap" then
        local k = CapKindForType("random") -- 或根据献祭类型
        ApplyUpperDelta(player, k, reward.amount or 5)
        if talker then talker:Say(string.format("永久%s上限 +%d", k, reward.amount or 5)) end
        return { cap_kind = k, cap_delta = reward.amount or 5 }
    elseif reward.type == "temp_buff" then
        ApplyTempBuff(player, reward)
        if talker then talker:Say(string.format("获得临时增益：%s", reward.effect or "未知")) end
    elseif reward.type == "blueprint" then
        GiveBlueprint(player, reward.tier or "basic")
        if talker then talker:Say("获得蓝图") end
    elseif reward.type == "temp_aura" then
        ApplyTempAura(player, reward)
        if talker then talker:Say(string.format("激活光环：%s", reward.effect or "未知")) end
    elseif reward.type == "summon" then
        ApplySummon(player, reward)
        if talker then talker:Say(string.format("召唤：%s", reward.creature or "未知")) end
    elseif reward.type == "temp_protection" then
        ApplyTempProtection(player, reward)
        if talker then talker:Say("获得超强护佑") end
    elseif reward.type == "luck_boost" then
        ApplyLuckBoost(player, reward)
        if talker then talker:Say("获得终极特权：献祭幸运加成") end
    elseif reward.type == "ultimate_boost" then
        ApplyUltimateBoost(player, reward)
        if talker then talker:Say("获得终极强化与称号") end
    elseif reward.type == "myth_aura" then
        ApplyMythAura(player, reward)
        if talker then talker:Say("激活神话领域") end
    end

    return nil
end

local function ApplyReward(player, t, tier, prefer_grade)
    local talker = player.components.talker
    local announce = GLOBAL.CARAVAN_MOD_ANNOUNCE or function() end
    local function reward_text(txt)
        if talker then talker:Say(txt) end
        if tier == "big" or tier == "super" or tier == "legend" or tier == "myth" then
            announce("献祭获得奖励："..txt)
        end
    end

    -- 统一物祭反馈文案到所有档位
    local rt = (prefer_grade and ("（物祭:%s级，偏向高稀有资源）")):format(tostring(prefer_grade)) or ""

    local pool = REWARD_POOLS[tier] or REWARD_POOLS.small
    local total_weight = 0
    for _, r in ipairs(pool) do total_weight = total_weight + (r.weight or 1) end

    local roll = math.random() * total_weight
    local current = 0
    local selected = nil

    for _, r in ipairs(pool) do
        current = current + (r.weight or 1)
        if roll <= current then
            selected = r
            break
        end
    end

    if not selected then selected = pool[1] end

    -- 应用奖励
    local info = ApplySpecificReward(player, selected, prefer_grade)

    -- 统一提示（所有档位都显示物祭信息）
    local tier_names = {small="小幅", mid="中等", big="大", super="超级", legend="传说", myth="神话"}
    reward_text(string.format("%s奖励%s", tier_names[tier] or tier, rt))

    return info
end
-- 奖励实现函数
function ApplyTempBuff(player, reward)
    local effect = reward.effect or "speed"
    local value = reward.value or 0.1
    local duration = reward.duration or 60

    if effect == "speed" then
        if player.components.locomotor then
            player.components.locomotor:SetExternalSpeedMultiplier(player, "sacrifice_buff", 1 + value)
            player:DoTaskInTime(duration, function()
                if player and player.components and player.components.locomotor then
                    player.components.locomotor:RemoveExternalSpeedMultiplier(player, "sacrifice_buff")
                end
            end)
        end
    elseif effect == "all_regen" then
        -- 全属性恢复加速
        local function boost_regen()
            if not player or not player.components then return end
            if player.components.health and not player.components.health:IsDead() then
                player.components.health:DoDelta(value, true, "sacrifice_regen")
            end
            if player.components.sanity then
                player.components.sanity:DoDelta(value * 2, true)
            end
            if player.components.hunger then
                player.components.hunger:DoDelta(value * 3, true)
            end
        end

        local task_count = 0
        local max_tasks = math.floor(duration / 5)
        player._sacrifice_regen_task = player:DoPeriodicTask(5, function()
            task_count = task_count + 1
            if task_count >= max_tasks then
                if player._sacrifice_regen_task then
                    player._sacrifice_regen_task:Cancel()
                    player._sacrifice_regen_task = nil
                end
                return
            end
            boost_regen()
        end)
    end
end

local function ApplyTempAura(player, reward)
    local effect = reward.effect or "team_speed"
    local value = reward.value or 0.1
    local duration = reward.duration or 120
    local range = reward.range or 6

    if effect == "team_speed" then
        local beneficiaries = {}
        local function apply_team_buff()
            if not player or not player.Transform then return end
            local x, y, z = player.Transform:GetWorldPosition()
            local players = GLOBAL.TheSim:FindEntities(x, y, z, range, {"player"})
            for _, p in ipairs(players) do
                if p ~= player and p.components and p.components.locomotor then
                    p.components.locomotor:SetExternalSpeedMultiplier(p, "sacrifice_team_aura", 1 + value)
                    table.insert(beneficiaries, p)
                end
            end
        end

        apply_team_buff()
        player:DoTaskInTime(duration, function()
            for _, p in ipairs(beneficiaries) do
                if p and p.components and p.components.locomotor then
                    p.components.locomotor:RemoveExternalSpeedMultiplier(p, "sacrifice_team_aura")
                end
            end
        end)
    end
end

local function GiveBlueprint(player, tier)
    -- 选择真正有价值的原版高阶配方，已掌握则给补偿
    local blueprints = {
        basic = { -- 罕见但原版
            "backpack", "spear", "log_suit"
        },
        advanced = { -- 科技、魔法、照明
            "minerhat", "lantern", "nightlight", "purpleamulet"
        },
        high = { -- 魔法/远古高阶
            "firestaff", "icestaff", "orangestaff", "telestaff", "greenamulet", "yellowamulet"
        }
    }

    local pool = blueprints[tier] or blueprints.basic
    local bp = pool[math.random(1, #pool)]

    if player.components.builder then
        if not player.components.builder:KnowsRecipe(bp) then
            player.components.builder:UnlockRecipe(bp)
        else
            -- 如果已知，给一些补偿资源（更慷慨一些）
            SpawnStackToPlayer(player, "goldnugget", 10)
            SpawnStackToPlayer(player, "gears", 1)
        end
    end
end

local function ApplySummon(player, reward)
    local creature = reward.creature or "hound"
    local count = reward.count or 1
    local duration = reward.duration or 60
    local friendly = reward.friendly or false

    local x, y, z = player.Transform:GetWorldPosition()
    local spawned = {}

    for i = 1, count do
        local angle = (i - 1) * (2 * math.pi / count)
        local dx, dz = math.cos(angle) * 3, math.sin(angle) * 3
        local ent = GLOBAL.SpawnPrefab(creature)
        if ent then
            ent.Transform:SetPosition(x + dx, y, z + dz)
            if friendly then
                -- 友好化：跟随并不误伤
                if ent.components.follower then
                    ent.components.follower:SetLeader(player)
                end
                if ent.components.combat then
                    ent.components.combat:SetTarget(nil)
                end
                ent:AddTag("playerally")
                ent._sacrifice_friendly = true
                ent._sacrifice_owner = player
            end
            table.insert(spawned, ent)
        end
    end

    -- 定时清理
    player:DoTaskInTime(duration, function()
        for _, ent in ipairs(spawned) do
            if ent and ent:IsValid() then
                if ent.components.follower then ent.components.follower:SetLeader(nil) end
                ent:Remove()
            end
        end
    end)
end
local function ApplyTempProtection(player, reward)
    local effects = reward.effects or {"nightvision", "waterproof", "insulation"}
    local duration = reward.duration or 300

    -- 夜视
    if table.concat(effects, ","):find("nightvision", 1, true) and player.components.playervision then
        player.components.playervision:ForceNightVision(true)
        player:DoTaskInTime(duration, function()
            if player and player.components and player.components.playervision then
                player.components.playervision:ForceNightVision(false)
            end
        end)
    end

    -- 防雨（简化：强制迅速变干+短期抗雨）
    if table.concat(effects, ","):find("waterproof", 1, true) and player.components.moisture then
        player.components.moisture:DoDelta(-100)
    end

    -- 温度保护（简化：短期无敌+轻回温）
    if table.concat(effects, ","):find("insulation", 1, true) then
        if player.components.health then
            player.components.health:SetInvincible(true)
            player:DoTaskInTime(math.min(3, duration), function()
                if player and player.components and player.components.health then
                    player.components.health:SetInvincible(false)
                end
            end)
        end
        if player.components.temperature then
            local cur = player.components.temperature:GetCurrent() or 20
            local target = cur + (player.components.temperature:GetCurrent() < 15 and 10 or 0)
            player.components.temperature:SetTemperature(target)
        end
    end
end

local function ApplyLuckBoost(player, reward)
    local value = reward.value or 0.2
    local duration = reward.duration or (20 * GLOBAL.TUNING.TOTAL_DAY_TIME)

    -- 简化实现：记录到玩家组件中
    if player.components.modplayer_sacrifice then
        player.components.modplayer_sacrifice:AddLuckBoost(value, duration)
    end
end

local function ApplyUltimateBoost(player, reward)
    local caps = reward.caps or {health=50, sanity=50, hunger=50}
    local title = reward.title or "幸运之神"

    -- 永久上限提升
    for kind, amount in pairs(caps) do
        ApplyUpperDelta(player, kind, amount)
    end

    -- 称号（简化实现：记录到组件）
    if player.components.modplayer_sacrifice then
        player.components.modplayer_sacrifice:SetTitle(title)
    end
end

local function ApplyMythAura(player, reward)
    local effects = reward.effects or {"shield", "speed", "efficiency"}
    local duration = reward.duration or 180
    local range = reward.range or 8

    local function apply_myth_effects()
        if not player or not player.Transform then return end
        local x, y, z = player.Transform:GetWorldPosition()
        local entities = GLOBAL.TheSim:FindEntities(x, y, z, range, {"player"})

        for _, ent in ipairs(entities) do
            if ent.components then
                for _, effect in ipairs(effects) do
                    if effect == "shield" and ent.components.health then
                        -- 简化护盾：临时减伤
                        ent._myth_shield_time = duration
                    elseif effect == "speed" and ent.components.locomotor then
                        ent.components.locomotor:SetExternalSpeedMultiplier(ent, "myth_aura", 1.5)
                    elseif effect == "efficiency" then
                        -- 工作效率提升（简化）
                        ent._myth_efficiency_time = duration
                    end
                end
            end
        end
    end

    apply_myth_effects()

    -- 定时清理
    player:DoTaskInTime(duration, function()
        if not player or not player.Transform then return end
        local x, y, z = player.Transform:GetWorldPosition()
        local entities = GLOBAL.TheSim:FindEntities(x, y, z, range * 2, {"player"})

        for _, ent in ipairs(entities) do
            if ent.components and ent.components.locomotor then
                ent.components.locomotor:RemoveExternalSpeedMultiplier(ent, "myth_aura")
            end
            ent._myth_shield_time = nil
            ent._myth_efficiency_time = nil
        end
    end)
end

-- 失败惩罚：从10种中随机一种
local function ApplyRandomPenalty(player, t)
    local pen = Sac().PENALTY or {}
    local talker = player.components.talker

    local function say(msg) if talker then talker:Say(msg) end end

    local function capdown(kind, delta)
        if delta ~= 0 then ApplyUpperDelta(player, kind, delta) end
    end

    local opts = {}

    -- 1. 永久上限减少（按献祭类型）
    table.insert(opts, function()
        if t == "blood" then capdown("health", -(pen.UPPER_CAP_DEBUFF.blood.health or 5))
        elseif t == "soul" then capdown("sanity", -(pen.UPPER_CAP_DEBUFF.soul.sanity or 10))
        elseif t == "hunger" then capdown("hunger", -(pen.UPPER_CAP_DEBUFF.hunger.hunger or 15))
        else capdown("health", -5) end
        say("失败：永久上限下降")
    end)

    -- 2. 背包物品全部掉落（装备不掉）
    table.insert(opts, function()
        local inv = player.components.inventory
        if inv then
            local overflow = inv:GetOverflowContainer() -- 背包容器
            if overflow then
                local n = overflow:GetNumSlots() or 0
                for i=1,n do
                    local it = overflow:RemoveItemBySlot(i)
                    if it then
                        local x,y,z = player.Transform:GetWorldPosition()
                        it.Transform:SetPosition(x + math.random(-2,2), y, z + math.random(-2,2))
                    end
                end
            end
        end
        say("失败：背包物品散落一地")
    end)

    -- 3. 体温异常 10s
    table.insert(opts, function()
        local temp = player.components.temperature
        if temp then
            local cur = temp:GetCurrent() or 20
            local target = cur + (math.random() < 0.5 and -30 or 30)
            temp:SetTemperature(target)
            player:DoTaskInTime(10, function() if player and player.components and player.components.temperature then player.components.temperature:SetTemperature(cur) end end)
            say("失败：体温异常")
        end
    end)

    -- 4. 敌对生物召唤
    table.insert(opts, function()
        local x,y,z = player.Transform:GetWorldPosition()
        local function spawn(prefab, dx, dz)
            local e = GLOBAL.SpawnPrefab(prefab)
            if e then e.Transform:SetPosition(x+dx, y, z+dz) end
        end
        local count = math.random(2,4)
        for i=1,count do spawn(math.random()<0.5 and "hound" or "spider_warrior", math.random(-3,3), math.random(-3,3)) end
        say("失败：敌对生物来袭")
    end)

    -- 5. 工具损坏 1-2件
    table.insert(opts, function()
        local inv = player.components.inventory
        local candidates = {}
        local function collect_from_container(container)
            if not container then return end
            local n = container:GetNumSlots() or 0
            for i=1,n do
                local it = container:GetItemInSlot(i)
                if it and it.components and it.components.finiteuses then table.insert(candidates, it) end
            end
        end
        if inv then
            collect_from_container(inv:GetOverflowContainer())
        end
        local picks = math.min(#candidates, math.random(1,2))
        for i=1,picks do
            local idx = math.random(1,#candidates)
            local it = table.remove(candidates, idx)
            if it and it.components.finiteuses then it.components.finiteuses:SetPercent(0) end
        end
        say("失败：工具损坏")
    end)

    -- 6. 负面光环：移速-30% 120s
    table.insert(opts, function()
        if player.components.locomotor then
            player.components.locomotor:SetExternalSpeedMultiplier(player, "sacrifice_penalty", 0.7)
            player:DoTaskInTime(120, function()
                if player and player.components and player.components.locomotor then
                    player.components.locomotor:RemoveExternalSpeedMultiplier(player, "sacrifice_penalty")
                end
            end)
            say("失败：行动迟缓")
        end
    end)

    -- 7. 理智崩溃
    table.insert(opts, function()
        if player.components.sanity then
            local cur = player.components.sanity.current or 0
            player.components.sanity:DoDelta(-cur)
            say("失败：理智崩溃")
        end
    end)

    -- 8. 饥饿诅咒（记录状态，效果由后续实现）
    table.insert(opts, function()
        ApplyCurse(player, "gluttony", (pen.CURSE_DURATION_DAYS and pen.CURSE_DURATION_DAYS.gluttony) or 2)
        say("失败：饥饿诅咒")
    end)

    -- 9. 黑暗诅咒（记录状态）
    table.insert(opts, function()
        ApplyCurse(player, "darkness", 1)
        say("失败：黑暗诅咒")
    end)

    -- 10. 随机传送（近距离）
    table.insert(opts, function()
        local x,y,z = player.Transform:GetWorldPosition()
        local angle = math.random()*2*math.pi
        local dist = math.random(50,150)
        local nx, nz = x + math.cos(angle)*dist, z + math.sin(angle)*dist
        if player.Physics then player.Physics:Teleport(nx, 0, nz) else player.Transform:SetPosition(nx, y, nz) end
        -- 3秒保护
        if player.components.health then
            player.components.health:SetInvincible(true)
            player:DoTaskInTime(3, function()
                if player and player.components and player.components.health then
                    player.components.health:SetInvincible(false)
                end
            end)
        end
        say("失败：被随机传送")
    end)

    -- 执行
    local pick = math.random(1, #opts)
    opts[pick]()
end

-- 对外主入口：执行一次献祭
-- opt: { item=item, confirm_token=token }
function W.DoSpin(player, t, opt)
    if not (player and player:IsValid() and player.components) then return false, "无效玩家" end
    local comp = player.components.modplayer_sacrifice
    if not comp then return false, "系统未初始化" end

    -- 限制
    local ok, reason = comp:CanSpin()
    if not ok then return false, reason end

    -- 资源检查
    if not EnoughResource(player, t) then return false, "资源不足" end

    -- 特殊：item 需要物品且识别稀有度
    local grade = nil
    if t == "item" then
        local item = opt and opt.item or (player.components.inventory and player.components.inventory:GetActiveItem())
        if not item then return false, "需要手持或指定物品" end
        local explain = Rarity.Explain(item)
        grade = explain and explain.grade or "C"
        -- 不满足最低稀有度时禁止
        local minr = Sac().COSTS.ITEM_MIN_RARITY or "C"
        local order = {C=1, B=2, A=3, S=4}
        if (order[grade] or 1) < (order[minr] or 1) then
            return false, "物品稀有度过低"
        end
        opt.item = item
    end

    -- 高风险确认（致死或会掉上限）
    local need_confirm = false
    if (Sac().LIMITS.REQUIRE_CONFIRM_FOR_HIGH_RISK ~= false) then
        if t == "blood" then
            -- 可能致死
            need_confirm = true
        else
            -- 失败惩罚存在上限减少
            need_confirm = true
        end
    end

    if need_confirm and (not opt or not opt.confirm_token) then
        local token = tostring(math.random(100000,999999))
        comp._pending = { t=t, token=token, time=Now()+30, grade=grade }
        return false, "CONFIRM:"..token
    end

    if comp._pending then
        local p = comp._pending
        if t ~= p.t then
            return false, "确认类型不匹配"
        end
        if not (opt and opt.confirm_token and tostring(opt.confirm_token) == tostring(p.token)) or Now() > (p.time or 0) then
            return false, "确认超时或令牌无效"
        end
        comp._pending = nil
    end

    -- 扣代价
    ApplyCost(player, t, opt)

    -- 记录次数/冷却
    comp:MarkSpinUsed()

    -- 失败附加惩罚
    if Roll(Sac().PENALTY.FAIL_PROB or 0.1) then
        comp.stats.fails = (comp.stats.fails or 0) + 1
        ApplyRandomPenalty(player, t)
        comp:_PushHistory({ when=GetTime(), type=t, result="fail", grade=grade })
        return true, "失败受罚"
    end

    -- 奖励档位
    local tier = RollTier(player, t, grade)
    local info = ApplyReward(player, t, tier, grade)
    comp.stats.wins = (comp.stats.wins or 0) + 1
    comp.stats[t] = (comp.stats[t] or 0) + 1
    comp:_PushHistory({ when=GetTime(), type=t, result=tier, grade=grade, info=info })

    return true, tier
end

return W

