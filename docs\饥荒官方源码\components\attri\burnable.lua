---@meta

---@class component_burnable
---@field inst idk
---@field flammability idk
---@field fxdata idk
---@field fxlevel idk
---@field fxchildren ent[] # `SpawnFX` 生成火焰都会在这个表里
---@field fxoffset idk
---@field burning idk
---@field burntime idk
---@field extinguishimmediately idk
---@field smoldertimeremaining idk
---@field smoldering idk
---@field onignite idk
---@field onextinguish idk
---@field onburnt idk
---@field onsmoldering idk
---@field onstopsmoldering idk
---@field canlight idk
---@field lightningimmune idk
---@field nocharring idk
---@field ignorefuel idk
---@field fastextinguish idk
---@field task idk
---@field smolder_task idk
---@field fxprefab idk
---@field smoke idk
---@field controlled_burn { duration_creature: number, damage: number } # 在 `SpawnFX` 之前,传这个参数表,设置 `duration_creature` 为 `3` , 就可以和火女一样 用较小的火势点燃敌人, `damage` 传 `0` 就行, 一定要在 `SpawnFX` 之后置空这个表,注意是置空,也就是 `nil` ,而不是置成空表 `{}`, 否则生物死亡时,会点燃附近