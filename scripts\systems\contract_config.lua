-- 合约配置管理系统
-- 处理模组配置选项和运行时配置

local GLOBAL = rawget(_G, "GLOBAL") or _G

local ContractConfig = {}

-- 默认配置值
local DEFAULT_CONFIG = {
    -- 交付限制
    deliver_min_durability_percent = 0.8,
    deliver_min_freshness_percent = 0.5,
    
    -- 目标池配置
    min_reachability_score = 0.3,
    seasonal_bonus = 1.5,
    rarity_penalty = 0.5,
    mod_content_weight = 0.8,
    
    -- 合约生成配置
    contract_count = 3,
    difficulty_distribution = {
        easy = 0.4,
        normal = 0.4,
        hard = 0.2
    },
    
    -- 生命周期配置
    cache_duration = 300, -- 5分钟
    cleanup_interval = 30, -- 30秒
    integrity_check_interval = 600, -- 10分钟
    
    -- 事件配置
    kill_dedup_window = 5, -- 5秒
    collection_dedup_window = 5, -- 5秒
    craft_dedup_window = 1, -- 1秒
    
    -- 网络配置
    sync_retry_interval = 5, -- 5秒
    max_sync_retries = 3
}

-- 初始化配置系统
function ContractConfig:Init()
    print("[合约配置] 初始化配置管理系统")
    
    -- 加载模组配置
    self.config = self:LoadModConfiguration()
    
    -- 验证配置
    self:ValidateConfiguration()
    
    -- 应用配置到各个系统
    self:ApplyConfiguration()
end

-- 加载模组配置
function ContractConfig:LoadModConfiguration()
    local config = {}
    
    -- 复制默认配置（深拷贝子表）
    for key, value in pairs(DEFAULT_CONFIG) do
        if type(value) == "table" then
            config[key] = {}
            for k, v in pairs(value) do
                config[key][k] = v
            end
        else
            config[key] = value
        end
    end
    
    -- 从TUNING中读取已解析的模组配置（避免在非modmain上下文调用GetModConfigData）
    local caravan = GLOBAL.TUNING and GLOBAL.TUNING.CARAVAN or nil
    if caravan then
        -- 交付限制配置
        if caravan.DELIVER_MIN_DURABILITY ~= nil then
            config.deliver_min_durability_percent = caravan.DELIVER_MIN_DURABILITY
        end
        if caravan.DELIVER_MIN_FRESHNESS ~= nil then
            config.deliver_min_freshness_percent = caravan.DELIVER_MIN_FRESHNESS
        end

        -- 目标池配置
        if caravan.TARGET_REACHABILITY ~= nil then
            config.min_reachability_score = caravan.TARGET_REACHABILITY
        end
        if caravan.MOD_CONTENT_WEIGHT ~= nil then
            config.mod_content_weight = caravan.MOD_CONTENT_WEIGHT
        end

        -- 合约数量配置
        if caravan.CONTRACT_COUNT ~= nil then
            config.contract_count = caravan.CONTRACT_COUNT
        end

        -- 难度配置（如果需要）
        if caravan.DIFFICULTY == "easy" then
            config.difficulty_distribution = {easy = 0.6, normal = 0.3, hard = 0.1}
        elseif caravan.DIFFICULTY == "hard" then
            config.difficulty_distribution = {easy = 0.2, normal = 0.4, hard = 0.4}
        end

        -- 星尘倍率配置
        if caravan.FAVOR_MULTIPLIER ~= nil then
            config.favor_multiplier = caravan.FAVOR_MULTIPLIER
        end
    end
    
    print("[合约配置] 配置加载完成")
    return config
end

-- 验证配置
function ContractConfig:ValidateConfiguration()
    local issues = {}
    
    -- 验证百分比值
    if self.config.deliver_min_durability_percent < 0 or self.config.deliver_min_durability_percent > 1 then
        table.insert(issues, "deliver_min_durability_percent 超出范围 [0,1]")
        self.config.deliver_min_durability_percent = DEFAULT_CONFIG.deliver_min_durability_percent
    end
    
    if self.config.deliver_min_freshness_percent < 0 or self.config.deliver_min_freshness_percent > 1 then
        table.insert(issues, "deliver_min_freshness_percent 超出范围 [0,1]")
        self.config.deliver_min_freshness_percent = DEFAULT_CONFIG.deliver_min_freshness_percent
    end
    
    if self.config.min_reachability_score < 0 or self.config.min_reachability_score > 1 then
        table.insert(issues, "min_reachability_score 超出范围 [0,1]")
        self.config.min_reachability_score = DEFAULT_CONFIG.min_reachability_score
    end
    
    if self.config.mod_content_weight < 0 or self.config.mod_content_weight > 1 then
        table.insert(issues, "mod_content_weight 超出范围 [0,1]")
        self.config.mod_content_weight = DEFAULT_CONFIG.mod_content_weight
    end
    
    -- 验证数值范围
    if self.config.contract_count < 1 or self.config.contract_count > 10 then
        table.insert(issues, "contract_count 超出合理范围 [1,10]")
        self.config.contract_count = DEFAULT_CONFIG.contract_count
    end
    
    -- 验证难度分布
    if self.config.difficulty_distribution then
        local total = 0
        for _, percent in pairs(self.config.difficulty_distribution) do
            total = total + percent
        end
        if math.abs(total - 1.0) > 0.01 then
            if total > 0 then
                -- 自动归一化，避免无谓的报错
                for k, v in pairs(self.config.difficulty_distribution) do
                    self.config.difficulty_distribution[k] = v / total
                end
                print(string.format("[合约配置] 已自动归一化难度分布: easy=%.2f, normal=%.2f, hard=%.2f",
                    self.config.difficulty_distribution.easy or 0,
                    self.config.difficulty_distribution.normal or 0,
                    self.config.difficulty_distribution.hard or 0
                ))
            else
                table.insert(issues, "difficulty_distribution 总和为0，使用默认值")
                self.config.difficulty_distribution = DEFAULT_CONFIG.difficulty_distribution
            end
        end
    end
    
    if #issues > 0 then
        print("[合约配置] 发现配置问题:", table.concat(issues, ", "))
    else
        print("[合约配置] 配置验证通过")
    end
end

-- 应用配置到各个系统
function ContractConfig:ApplyConfiguration()
    -- 应用到TUNING系统
    if not GLOBAL.TUNING.CARAVAN then
        GLOBAL.TUNING.CARAVAN = {}
    end
    
    GLOBAL.TUNING.CARAVAN.CONTRACT_COUNT = self.config.contract_count
    GLOBAL.TUNING.CARAVAN.DELIVER_MIN_DURABILITY = self.config.deliver_min_durability_percent
    GLOBAL.TUNING.CARAVAN.DELIVER_MIN_FRESHNESS = self.config.deliver_min_freshness_percent
    
    -- 目标池配置
    GLOBAL.TUNING.CARAVAN.TARGET_CONFIG = {
        min_reachability_score = self.config.min_reachability_score,
        seasonal_bonus = self.config.seasonal_bonus,
        rarity_penalty = self.config.rarity_penalty,
        mod_content_weight = self.config.mod_content_weight
    }
    
    -- 难度分布配置
    GLOBAL.TUNING.CARAVAN.DIFFICULTY_DISTRIBUTION = self.config.difficulty_distribution
    
    -- 事件系统配置
    GLOBAL.TUNING.CARAVAN.EVENT_CONFIG = {
        kill_dedup_window = self.config.kill_dedup_window,
        collection_dedup_window = self.config.collection_dedup_window,
        craft_dedup_window = self.config.craft_dedup_window
    }
    
    -- 生命周期配置
    GLOBAL.TUNING.CARAVAN.LIFECYCLE_CONFIG = {
        cache_duration = self.config.cache_duration,
        cleanup_interval = self.config.cleanup_interval,
        integrity_check_interval = self.config.integrity_check_interval
    }
    
    print("[合约配置] 配置已应用到TUNING系统")
end

-- 获取配置值
function ContractConfig:GetConfig(key)
    return self.config[key]
end

-- 获取所有配置
function ContractConfig:GetAllConfig()
    return self.config
end

-- 更新配置值
function ContractConfig:UpdateConfig(key, value)
    if self.config[key] ~= nil then
        local old_value = self.config[key]
        self.config[key] = value
        
        print("[合约配置] 更新配置:", key, old_value, "->", value)
        
        -- 重新应用配置
        self:ApplyConfiguration()
        
        return true
    else
        print("[合约配置] 警告：未知配置键:", key)
        return false
    end
end

-- 重置配置为默认值
function ContractConfig:ResetToDefaults()
    self.config = {}
    for key, value in pairs(DEFAULT_CONFIG) do
        if type(value) == "table" then
            self.config[key] = {}
            for k, v in pairs(value) do
                self.config[key][k] = v
            end
        else
            self.config[key] = value
        end
    end
    
    self:ApplyConfiguration()
    print("[合约配置] 配置已重置为默认值")
end

-- 获取交付验证配置
function ContractConfig:GetDeliveryValidationConfig()
    return {
        min_durability_percent = self.config.deliver_min_durability_percent,
        min_freshness_percent = self.config.deliver_min_freshness_percent
    }
end

-- 获取目标池配置
function ContractConfig:GetTargetPoolConfig()
    return {
        min_reachability_score = self.config.min_reachability_score,
        seasonal_bonus = self.config.seasonal_bonus,
        rarity_penalty = self.config.rarity_penalty,
        mod_content_weight = self.config.mod_content_weight
    }
end

-- 获取难度分布配置
function ContractConfig:GetDifficultyDistribution()
    return self.config.difficulty_distribution
end

-- 获取事件系统配置
function ContractConfig:GetEventConfig()
    return {
        kill_dedup_window = self.config.kill_dedup_window,
        collection_dedup_window = self.config.collection_dedup_window,
        craft_dedup_window = self.config.craft_dedup_window
    }
end

-- 检查是否启用了某个功能
function ContractConfig:IsFeatureEnabled(feature)
    local feature_flags = {
        mod_content = self.config.mod_content_weight > 0,
        durability_check = self.config.deliver_min_durability_percent > 0,
        freshness_check = self.config.deliver_min_freshness_percent > 0,
        strict_reachability = self.config.min_reachability_score > 0.5
    }
    
    return feature_flags[feature] or false
end

-- 获取配置摘要
function ContractConfig:GetConfigSummary()
    return {
        contract_count = self.config.contract_count,
        delivery_restrictions = {
            durability = self.config.deliver_min_durability_percent,
            freshness = self.config.deliver_min_freshness_percent
        },
        target_pool = {
            reachability = self.config.min_reachability_score,
            mod_weight = self.config.mod_content_weight
        },
        difficulty = self.config.difficulty_distribution
    }
end

-- 导出配置到字符串
function ContractConfig:ExportConfig()
    local export_data = {}
    for key, value in pairs(self.config) do
        if type(value) ~= "table" then
            export_data[key] = value
        end
    end
    
    return GLOBAL.json and GLOBAL.json.encode(export_data) or "JSON不可用"
end

-- 从字符串导入配置
function ContractConfig:ImportConfig(config_string)
    if not GLOBAL.json then
        print("[合约配置] 错误：JSON不可用")
        return false
    end
    
    local success, import_data = pcall(GLOBAL.json.decode, config_string)
    if not success then
        print("[合约配置] 错误：配置字符串解析失败")
        return false
    end
    
    local updated_count = 0
    for key, value in pairs(import_data) do
        if self.config[key] ~= nil and type(value) == type(self.config[key]) then
            self.config[key] = value
            updated_count = updated_count + 1
        end
    end
    
    if updated_count > 0 then
        self:ValidateConfiguration()
        self:ApplyConfiguration()
        print("[合约配置] 导入了", updated_count, "个配置项")
        return true
    else
        print("[合约配置] 没有有效的配置项可导入")
        return false
    end
end

return ContractConfig
