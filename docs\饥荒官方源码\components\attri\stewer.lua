---@meta

---@class component_stewer
---@field inst ent # 挂载了组件stewer的实体，如烹饪锅
---@field done boolean # 是否完成烹饪
---@field targettime number|nil # 一个时刻，可用于表示烹饪完成的时刻，也可用于表示锅上食物腐烂的时刻
---@field task idk # 烹饪任务，腐烂任务等
---@field product ent # 产物，即料理实体
---@field product_spoilage number # 烹饪完成时，料理的新鲜度
---@field spoiledproduct string|"spoiled_food" # 锅上的食物不采集的腐烂产物
---@field spoiltime number # 烹饪完成的料理的腐烂时长
---@field keepspoilage boolean|nil # 默认新鲜度损坏一半，设置为true不会
---@field cooktimemult number|1 # 烹饪时长倍率，如大厨锅为0.8
---@field chef_id userid # 做饭角色的userid
---@field ingredient_prefabs table<string> # 食材表，即放入锅中的四个食材
