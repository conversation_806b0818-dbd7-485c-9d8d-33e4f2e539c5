---@meta

---@class component_edible
---@field inst idk
---@field healthvalue idk
---@field hungervalue idk
---@field sanityvalue idk
---@field foodtype string # 想让牛可以吃: `FOODTYPE.ROUGHAGE` <br> 想让沃比可以吃 `secondaryfoodtype = FOODTYPE.MONSTER`
---@field secondaryfoodtype idk
---@field oneaten fun(this:ent, eater:ent) # `SetOnEatenFn` 设置的
---@field degrades_with_spoilage idk
---@field gethealthfn idk
---@field getsanityfn idk
---@field temperaturedelta idk
---@field temperatureduration idk
---@field chill idk
---@field nochill idk
---@field stale_hunger idk
---@field stale_health idk
---@field spoiled_hunger idk
---@field spoiled_health idk
---@field spice idk