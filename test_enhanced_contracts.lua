-- 测试完善后的合约系统
print("=== 测试完善后的合约系统 ===")

-- 模拟全局环境
local function CreateMockGlobals()
    GLOBAL = GLOBAL or {}
    GLOBAL.AllPlayers = {}
    GLOBAL.TheWorld = {
        state = { cycles = 1 },
        ismastersim = true,
        components = {},
        ListenForEvent = function(self, event, fn) 
            print("监听事件:", event)
        end,
        DoTaskInTime = function(self, time, fn)
            print("延迟任务:", time, "秒")
            fn()
        end,
        DoPeriodicTask = function(self, interval, fn)
            print("定期任务:", interval, "秒间隔")
            return { task = fn }
        end
    }
    GLOBAL.TheNet = {
        GetIsServer = function() return true end,
        Announce = function(msg) print("公告:", msg) end
    }
    GLOBAL.TUNING = {
        CARAVAN = {
            CONTRACT_COUNT = 5,
            MAX_CARAVAN_WAIT_DAYS = 7
        }
    }
    
    -- 模拟玩家
    local mock_player = {
        name = "测试玩家",
        prefab = "wilson",
        components = {
            inventory = {
                maxslots = 15,
                itemslots = {},
                RemoveItemBySlot = function(self, slot)
                    print("移除物品槽位:", slot)
                    self.itemslots[slot] = nil
                end
            },
            talker = {
                Say = function(self, msg) print("玩家说:", msg) end
            }
        },
        HasTag = function(self, tag) return tag == "player" end,
        IsValid = function() return true end,
        ListenForEvent = function(self, event, fn)
            print("玩家监听事件:", event)
        end,
        GetDistanceSqToInst = function(self, other)
            return 100 -- 模拟距离
        end
    }
    
    -- 添加一些测试物品到背包
    mock_player.components.inventory.itemslots[1] = {
        prefab = "goldnugget",
        components = {
            stackable = {
                StackSize = function() return 15 end,
                Get = function(self, amount) 
                    print("从堆叠中取出:", amount)
                end
            }
        }
    }
    
    table.insert(GLOBAL.AllPlayers, mock_player)
    return mock_player
end

-- 创建模拟环境
local mock_player = CreateMockGlobals()

-- 加载合约系统
local ModWorld = require("scripts/components/modworld_global")

-- 创建世界组件实例
local world_comp = ModWorld(GLOBAL.TheWorld)
GLOBAL.TheWorld.components.modworld_global = world_comp

print("\n=== 测试合约生成 ===")
world_comp:GenerateContracts()

print("\n=== 显示合约列表 ===")
world_comp:CmdShowContracts(mock_player)

print("\n=== 测试合约验证 ===")
world_comp:ValidateContracts()

print("\n=== 测试击杀合约 ===")
-- 模拟击杀蜘蛛
local mock_spider = {
    prefab = "spider",
    HasTag = function(self, tag) return tag ~= "spiderling" end
}
for i = 1, 3 do
    print("击杀蜘蛛", i)
    world_comp:OnEntityKilled(mock_spider, mock_player)
end

print("\n=== 测试建设合约 ===")
-- 模拟建造猪屋
local mock_pighouse = {
    prefab = "pighouse",
    components = {
        spawner = true,
        finiteuses = {
            GetPercent = function() return 1.0 end
        }
    }
}
print("建造猪屋")
world_comp:OnStructureBuilt(mock_pighouse, mock_player)

print("\n=== 测试采集合约 ===")
-- 模拟采集浆果
local mock_berries = {
    {
        prefab = "berries",
        components = {
            stackable = {
                StackSize = function() return 5 end
            }
        }
    }
}
print("采集浆果")
world_comp:OnItemPicked(nil, mock_berries, mock_player)

print("\n=== 测试制作合约 ===")
-- 模拟制作斧头
local mock_axe = {
    prefab = "axe"
}
print("制作斧头")
world_comp:OnItemCrafted(mock_axe, mock_player)

print("\n=== 测试交付合约 ===")
-- 查找金块交付合约
local gold_contract_index = nil
for i, contract in ipairs(world_comp.contracts) do
    if contract.type == "deliver" and contract.target_data.item == "goldnugget" then
        gold_contract_index = i
        break
    end
end

if gold_contract_index then
    print("尝试交付金块合约")
    world_comp:CmdDeliverContract(mock_player, tostring(gold_contract_index))
else
    print("未找到金块交付合约")
end

print("\n=== 测试定期检查 ===")
world_comp:PeriodicContractCheck()

print("\n=== 最终合约状态 ===")
world_comp:CmdShowContracts(mock_player)

print("\n=== 测试完成 ===")
