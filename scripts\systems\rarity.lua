-- 动态稀有度识别系统（S/A/B/C）
-- 兼容跨模组：标签/组件/名称启发 + 核心物品评分表

local GLOBAL = rawget(_G, "GLOBAL") or _G
local TUNING = GLOBAL.TUNING or {}

local R = {}

-- 核心物品基准分（可按需扩展/覆写）
local KNOWN_ITEM_RARITY = {
    -- S：Boss/一次性/极稀有
    deerclops_eyeball = 50,
    walrus_tusk = 45,
    shadow_atrium = 50,
    ancient_key = 50,
    royal_jelly = 45,

    -- A：季节/门槛/低效率再生
    gears = 30,
    nightmarefuel = 35,
    thulecite = 40,
    redgem = 25,
    bluegem = 25,
    purplegem = 30,

    -- B：需要准备/有风险
    livinglog = 15,
    silk = 12,
    pigskin = 15,
    goldnugget = 10,

    -- C：常见易得
    log = 0, rocks = 0, cutgrass = 0, twigs = 0, flint = 0, berries = 0,
}

local function add(tbl, v) tbl[#tbl+1] = v end

local function score_to_grade(score)
    if score >= 40 then return "S" end
    if score >= 25 then return "A" end
    if score >= 10 then return "B" end
    return "C"
end

-- 返回 {grade, score, factors = { {name=..., delta=...}, ... }}
function R.Explain(item)
    local prefab = item and item.prefab or nil
    local factors, score = {}, 0
    if not prefab then
        add(factors, {name="no_prefab", delta=0})
        return { grade = "C", score = 0, factors = factors }
    end

    local base = KNOWN_ITEM_RARITY[prefab] or 0
    score = score + base
    if base ~= 0 then add(factors, {name="base["..prefab.."]", delta=base}) end

    -- 标签启发：兼容其他模组
    if item.HasTag then
        local function bump(tag, delta)
            if item:HasTag(tag) then score = score + delta; add(factors, {name="tag:"..tag, delta=delta}) end
        end
        bump("boss_drop", 30)
        bump("ancient", 25)
        bump("magic", 20)
        bump("rare", 15)
        bump("seasonal", 15)
        bump("blueprint", 10)
        bump("cave", 10)
    end

    -- 组件启发
    if item.components then
        if item.components.spellcaster then score = score + 20; add(factors, {name="comp:spellcaster", delta=20}) end
        if item.components.armor and (item.components.armor.maxcondition or 0) > 1000 then
            score = score + 15; add(factors, {name="comp:armor_high", delta=15})
        end
        if item.components.weapon and (item.components.weapon.damage or 0) > 50 then
            score = score + 10; add(factors, {name="comp:weapon_high", delta=10})
        end
    end

    -- 名称启发（prefab 字符串）
    local name_lower = string.lower(prefab)
    if string.find(name_lower, "boss") or string.find(name_lower, "epic") then
        score = score + 25; add(factors, {name="name:boss/epic", delta=25})
    end
    if string.find(name_lower, "rare") or string.find(name_lower, "legendary") then
        score = score + 20; add(factors, {name="name:rare/legendary", delta=20})
    end
    if string.find(name_lower, "ancient") or string.find(name_lower, "ruin") then
        score = score + 15; add(factors, {name="name:ancient/ruin", delta=15})
    end

    local grade = score_to_grade(score)
    return { grade = grade, score = score, factors = factors }
end

function R.GetItemRarity(item)
    local res = R.Explain(item)
    return res.grade
end

-- 暴露核心表以便外部扩展/覆盖
function R.GetKnownTable()
    return KNOWN_ITEM_RARITY
end

return R

