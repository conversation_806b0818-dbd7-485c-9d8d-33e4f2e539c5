-- 合约目标池动态构建系统
-- 支持跨模组内容的自动发现和纳入

local GLOBAL = rawget(_G, "GLOBAL") or _G

local ContractTargets = {}

-- 目标池缓存
ContractTargets.cached_pools = {
    kill = {},
    collect = {},
    craft = {},
    last_update = 0,
    cache_duration = 300 -- 5分钟缓存
}

-- 难度权重配置
local DIFFICULTY_WEIGHTS = {
    easy = { min_weight = 1, max_weight = 3 },
    normal = { min_weight = 4, max_weight = 7 },
    hard = { min_weight = 8, max_weight = 10 }
}

-- 默认配置
local DEFAULT_CONFIG = {
    min_reachability_score = 0.3, -- 最低可达性分数
    seasonal_bonus = 1.5, -- 季节性奖励倍数
    rarity_penalty = 0.5, -- 稀有度惩罚倍数
    mod_content_weight = 0.8 -- 模组内容权重倍数
}

-- 初始化目标池系统
function ContractTargets:Init()
    print("[合约目标池] 初始化动态目标池系统")
    
    -- 加载配置
    self.config = self:LoadConfig()
    
    -- 初始化缓存
    self:ClearCache()
    
    -- 监听世界变化事件
    self:SetupEventListeners()
end

-- 加载配置
function ContractTargets:LoadConfig()
    local config = {}
    for k, v in pairs(DEFAULT_CONFIG) do
        config[k] = v
    end
    
    -- 从TUNING中读取自定义配置
    if GLOBAL.TUNING and GLOBAL.TUNING.CARAVAN and GLOBAL.TUNING.CARAVAN.TARGET_CONFIG then
        local custom_config = GLOBAL.TUNING.CARAVAN.TARGET_CONFIG
        for k, v in pairs(custom_config) do
            config[k] = v
        end
    end
    
    return config
end

-- 设置事件监听器
function ContractTargets:SetupEventListeners()
    if not GLOBAL.TheWorld then return end
    
    -- 监听季节变化
    GLOBAL.TheWorld:ListenForEvent("seasonchange", function()
        self:ClearCache()
        print("[合约目标池] 季节变化，清除缓存")
    end)
    
    -- 监听世界状态变化
    GLOBAL.TheWorld:ListenForEvent("cycleschanged", function()
        -- 每天清除一次缓存，确保目标池更新
        if (GLOBAL.TheWorld.state.cycles or 0) % 1 == 0 then
            self:ClearCache()
            print("[合约目标池] 新的一天，清除缓存")
        end
    end)
end

-- 清除缓存
function ContractTargets:ClearCache()
    self.cached_pools = {
        kill = {},
        collect = {},
        craft = {},
        last_update = 0,
        cache_duration = 300
    }
end

-- 获取击杀目标池
function ContractTargets:GetKillTargets(difficulty)
    if self:IsCacheValid("kill") then
        return self:FilterByDifficulty(self.cached_pools.kill, difficulty)
    end
    
    print("[合约目标池] 构建击杀目标池...")
    local targets = {}
    
    -- 遍历所有已注册的prefab
    for prefab_name, prefab_data in pairs(GLOBAL.Prefabs or {}) do
        if self:IsValidKillTarget(prefab_name, prefab_data) then
            local target_info = self:CreateKillTargetInfo(prefab_name, prefab_data)
            if target_info then
                table.insert(targets, target_info)
            end
        end
    end
    
    -- 添加静态目标（确保基础内容）
    self:AddStaticKillTargets(targets)
    
    -- 计算权重和可达性
    for _, target in ipairs(targets) do
        target.weight = self:CalculateKillTargetWeight(target)
        target.reachability = self:CalculateReachability(target)
    end
    
    -- 过滤不可达目标
    targets = self:FilterReachableTargets(targets)
    
    -- 缓存结果
    self.cached_pools.kill = targets
    self.cached_pools.last_update = GLOBAL.GetTime()
    
    print("[合约目标池] 击杀目标池构建完成，共", #targets, "个目标")
    return self:FilterByDifficulty(targets, difficulty)
end

-- 检查是否为有效的击杀目标
function ContractTargets:IsValidKillTarget(prefab_name, prefab_data)
    if not prefab_name or not prefab_data then return false end

    -- 排除玩家和建筑
    if prefab_name:find("player") or prefab_name:find("structure") then
        return false
    end

    -- 检查已知的生物类型（基于名称模式）
    local known_creatures = {
        "spider", "hound", "tentacle", "tallbird", "beefalo", "koalefant",
        "deerclops", "bearger", "moose", "dragonfly", "bee", "killer_bee",
        "frog", "pengull", "walrus", "little_walrus", "mactusk", "warg",
        "clockwork", "knight", "bishop", "rook", "shadow", "nightmare",
        "bat", "catcoon", "volt_goat", "lightninggoat", "pig", "bunnymen",
        "werepig", "ghost", "crawlinghorror", "terrorbeak", "eyeplant",
        "lureplant", "krampus", "slurper", "monkey", "prime_mate"
    }

    for _, creature in ipairs(known_creatures) do
        if prefab_name:find(creature) then
            return true
        end
    end

    -- 检查模组内容权重
    if self:IsModContent(prefab_name) then
        local mod_weight = self:GetModContentWeight()
        return mod_weight > 0 -- 如果模组权重大于0，则认为可达
    end

    return false
end

-- 检查是否为模组内容
function ContractTargets:IsModContent(prefab_name)
    -- 检查是否为原版内容
    local vanilla_creatures = {
        "spider", "spider_warrior", "spider_hider", "spider_spitter",
        "hound", "icehound", "firehound", "mutatedhound",
        "tentacle", "tentacle_pillar", "tallbird", "smallbird", "teenbird",
        "beefalo", "babybeefalo", "koalefant_summer", "koalefant_winter",
        "deerclops", "bearger", "moose", "dragonfly", "antlion", "toadstool",
        "bee", "killerbee", "frog", "pengull", "walrus", "little_walrus",
        "mactusk", "warg", "spat", "ewecus", "koalefant_summer", "koalefant_winter"
    }

    for _, vanilla in ipairs(vanilla_creatures) do
        if prefab_name:find(vanilla) then
            return false -- 原版内容
        end
    end

    return true -- 可能是模组内容
end

-- 获取模组内容权重
function ContractTargets:GetModContentWeight()
    if GLOBAL.TUNING and GLOBAL.TUNING.CARAVAN then
        return GLOBAL.TUNING.CARAVAN.MOD_CONTENT_WEIGHT or 0.8
    end
    return 0.8
end

-- 创建击杀目标信息
function ContractTargets:CreateKillTargetInfo(prefab_name, prefab_data)
    local display_name = self:GetDisplayName(prefab_name)
    if not display_name then return nil end
    
    return {
        prefab = prefab_name,
        name = display_name,
        type = "kill",
        base_goal = self:CalculateBaseGoal(prefab_name, "kill"),
        base_favor = self:CalculateBaseFavor(prefab_name, "kill"),
        base_rep = self:CalculateBaseRep(prefab_name, "kill"),
        source = self:GetPrefabSource(prefab_name),
        seasonal_factor = self:GetSeasonalFactor(prefab_name),
        rarity = self:GetRarityScore(prefab_name)
    }
end

-- 获取采集目标池
function ContractTargets:GetCollectTargets(difficulty)
    if self:IsCacheValid("collect") then
        return self:FilterByDifficulty(self.cached_pools.collect, difficulty)
    end
    
    print("[合约目标池] 构建采集目标池...")
    local targets = {}
    
    -- 遍历所有已注册的prefab，寻找可采集物品
    for prefab_name, prefab_data in pairs(GLOBAL.Prefabs or {}) do
        if self:IsValidCollectTarget(prefab_name, prefab_data) then
            local target_info = self:CreateCollectTargetInfo(prefab_name, prefab_data)
            if target_info then
                table.insert(targets, target_info)
            end
        end
    end
    
    -- 添加静态目标
    self:AddStaticCollectTargets(targets)
    
    -- 计算权重和可达性
    for _, target in ipairs(targets) do
        target.weight = self:CalculateCollectTargetWeight(target)
        target.reachability = self:CalculateReachability(target)
    end
    
    -- 过滤不可达目标
    targets = self:FilterReachableTargets(targets)
    
    -- 缓存结果
    self.cached_pools.collect = targets
    self.cached_pools.last_update = GLOBAL.GetTime()
    
    print("[合约目标池] 采集目标池构建完成，共", #targets, "个目标")
    return self:FilterByDifficulty(targets, difficulty)
end

-- 检查是否为有效的采集目标
function ContractTargets:IsValidCollectTarget(prefab_name, _)
    if not prefab_name then return false end

    -- 检查已知的可采集物品
    local known_collectibles = {
        "berries", "carrot", "mushroom", "flower", "grass", "twigs", "flint",
        "rocks", "nitre", "gold", "log", "cutgrass", "petals", "seeds",
        "honey", "silk", "gears", "gem", "marble", "thulecite", "ice",
        "cactus", "reeds", "bamboo", "vine", "coconut", "banana", "dragonfruit",
        "pomegranate", "eggplant", "corn", "pumpkin", "watermelon", "durian",
        "mandrake", "lightbulb", "spidergland", "stinger", "horn", "trunk"
    }

    for _, item in ipairs(known_collectibles) do
        if prefab_name:find(item) then
            return true
        end
    end

    -- 检查季节性可用性
    if self:IsSeasonallyAvailable(prefab_name) then
        return true
    end

    return false
end

-- 检查物品是否在当前季节可用
function ContractTargets:IsSeasonallyAvailable(prefab_name)
    if not GLOBAL.TheWorld or not GLOBAL.TheWorld.state then return true end

    local season = GLOBAL.TheWorld.state.season

    -- 季节性物品映射
    local seasonal_items = {
        spring = {"flower", "petals", "butterfly", "frog"},
        summer = {"cactus", "watermelon", "dragonfruit"},
        autumn = {"berries", "carrot", "mushroom", "acorn"},
        winter = {"ice", "pinecone"}
    }

    -- 检查是否为季节性物品
    if seasonal_items[season] then
        for _, item in ipairs(seasonal_items[season]) do
            if prefab_name:find(item) then
                return true
            end
        end
    end

    -- 全年可用的基础物品
    local year_round_items = {"twigs", "grass", "flint", "rocks", "log", "cutgrass"}
    for _, item in ipairs(year_round_items) do
        if prefab_name:find(item) then
            return true
        end
    end

    return false
end

-- 创建采集目标信息
function ContractTargets:CreateCollectTargetInfo(prefab_name, prefab_data)
    local display_name = self:GetDisplayName(prefab_name)
    if not display_name then return nil end
    
    return {
        item = prefab_name,
        name = display_name,
        type = "collect",
        base_goal = self:CalculateBaseGoal(prefab_name, "collect"),
        base_favor = self:CalculateBaseFavor(prefab_name, "collect"),
        base_rep = self:CalculateBaseRep(prefab_name, "collect"),
        source = self:GetPrefabSource(prefab_name),
        seasonal_factor = self:GetSeasonalFactor(prefab_name),
        rarity = self:GetRarityScore(prefab_name)
    }
end

-- 获取制作目标池
function ContractTargets:GetCraftTargets(difficulty)
    if self:IsCacheValid("craft") then
        return self:FilterByDifficulty(self.cached_pools.craft, difficulty)
    end
    
    print("[合约目标池] 构建制作目标池...")
    local targets = {}
    
    -- 从AllRecipes获取所有配方
    if GLOBAL.AllRecipes then
        for recipe_name, recipe_data in pairs(GLOBAL.AllRecipes) do
            if self:IsValidCraftTarget(recipe_name, recipe_data) then
                local target_info = self:CreateCraftTargetInfo(recipe_name, recipe_data)
                if target_info then
                    table.insert(targets, target_info)
                end
            end
        end
    end
    
    -- 添加静态目标
    self:AddStaticCraftTargets(targets)
    
    -- 计算权重和可达性
    for _, target in ipairs(targets) do
        target.weight = self:CalculateCraftTargetWeight(target)
        target.reachability = self:CalculateCraftReachability(target)
    end
    
    -- 过滤不可达目标
    targets = self:FilterReachableTargets(targets)
    
    -- 缓存结果
    self.cached_pools.craft = targets
    self.cached_pools.last_update = GLOBAL.GetTime()
    
    print("[合约目标池] 制作目标池构建完成，共", #targets, "个目标")
    return self:FilterByDifficulty(targets, difficulty)
end

-- 检查是否为有效的制作目标
function ContractTargets:IsValidCraftTarget(recipe_name, recipe_data)
    if not recipe_name or not recipe_data then return false end
    
    -- 排除管理员专用或作弊配方
    if recipe_data.nounlock or recipe_data.no_deconstruction then
        return false
    end
    
    -- 检查科技要求是否合理
    local tech_level = recipe_data.level or {}
    local total_tech = 0
    for _, level in pairs(tech_level) do
        total_tech = total_tech + (level or 0)
    end
    
    -- 排除科技要求过高的配方
    if total_tech > 10 then
        return false
    end
    
    return true
end

-- 创建制作目标信息
function ContractTargets:CreateCraftTargetInfo(recipe_name, recipe_data)
    local display_name = self:GetDisplayName(recipe_name)
    if not display_name then return nil end
    
    return {
        item = recipe_name,
        name = display_name,
        type = "craft",
        base_goal = self:CalculateBaseGoal(recipe_name, "craft"),
        base_favor = self:CalculateBaseFavor(recipe_name, "craft"),
        base_rep = self:CalculateBaseRep(recipe_name, "craft"),
        recipe = recipe_data,
        tech_level = self:CalculateTechLevel(recipe_data),
        source = self:GetRecipeSource(recipe_name),
        seasonal_factor = 1.0, -- 制作不受季节影响
        rarity = self:GetCraftRarityScore(recipe_name, recipe_data)
    }
end

-- 辅助方法

-- 检查缓存是否有效
function ContractTargets:IsCacheValid(pool_type)
    if not self.cached_pools[pool_type] or #self.cached_pools[pool_type] == 0 then
        return false
    end

    local current_time = GLOBAL.GetTime()
    return (current_time - self.cached_pools.last_update) < self.cached_pools.cache_duration
end

-- 按难度过滤目标
function ContractTargets:FilterByDifficulty(targets, difficulty)
    if not targets or not difficulty then return targets end

    local weights = DIFFICULTY_WEIGHTS[difficulty]
    if not weights then return targets end

    local filtered = {}
    for _, target in ipairs(targets) do
        if target.weight >= weights.min_weight and target.weight <= weights.max_weight then
            table.insert(filtered, target)
        end
    end

    return filtered
end

-- 过滤可达目标
function ContractTargets:FilterReachableTargets(targets)
    local filtered = {}
    for _, target in ipairs(targets) do
        if target.reachability >= self.config.min_reachability_score then
            table.insert(filtered, target)
        end
    end
    return filtered
end

-- 获取显示名称
function ContractTargets:GetDisplayName(prefab_name)
    -- 尝试从STRINGS获取本地化名称
    if GLOBAL.STRINGS and GLOBAL.STRINGS.NAMES then
        local name = GLOBAL.STRINGS.NAMES[string.upper(prefab_name)]
        if name then return name end
    end

    -- 生成友好的显示名称
    local display_name = prefab_name:gsub("_", " ")
    display_name = display_name:gsub("(%a)([%w_']*)", function(first, rest)
        return first:upper() .. rest:lower()
    end)

    return display_name
end

-- 获取prefab来源
function ContractTargets:GetPrefabSource(prefab_name)
    -- 简化的来源检测
    if GLOBAL.KnownModIndex then
        for mod_name, _ in pairs(GLOBAL.KnownModIndex:GetModsToLoad() or {}) do
            if mod_name ~= "workshop-" and mod_name ~= "thinking" then
                return "mod:" .. mod_name
            end
        end
    end
    return "base"
end

-- 获取配方来源
function ContractTargets:GetRecipeSource(recipe_name)
    return self:GetPrefabSource(recipe_name)
end

-- 计算基础目标数量
function ContractTargets:CalculateBaseGoal(prefab_name, contract_type)
    local base_goals = {
        kill = { default = 5, spider = 10, hound = 5, tentacle = 3, deerclops = 1 },
        collect = { default = 20, berries = 30, flint = 15, gold = 10 },
        craft = { default = 3, axe = 2, pickaxe = 2, spear = 5 }
    }

    local type_goals = base_goals[contract_type] or base_goals.kill

    for pattern, goal in pairs(type_goals) do
        if pattern ~= "default" and prefab_name:find(pattern) then
            return goal
        end
    end

    return type_goals.default
end

-- 计算基础星尘奖励
function ContractTargets:CalculateBaseFavor(prefab_name, contract_type)
    local base_favor = {
        kill = { default = 5, boss = 20, mini_boss = 12 },
        collect = { default = 4, rare = 10 },
        craft = { default = 6, advanced = 15 }
    }

    local type_favor = base_favor[contract_type] or base_favor.kill

    -- 检查是否为BOSS
    if prefab_name:find("boss") or prefab_name:find("deerclops") or
       prefab_name:find("bearger") or prefab_name:find("moose") then
        return type_favor.boss or type_favor.default * 3
    end

    return type_favor.default
end

-- 计算基础声望奖励
function ContractTargets:CalculateBaseRep(prefab_name, contract_type)
    local base_rep = {
        kill = { pig = 2, bunnymen = 1 },
        collect = { pig = 1 },
        craft = { pig = 2 }
    }

    return base_rep[contract_type] or base_rep.kill
end

-- 计算季节性因子
function ContractTargets:GetSeasonalFactor(prefab_name)
    if not GLOBAL.TheWorld or not GLOBAL.TheWorld.state then return 1.0 end

    local season = GLOBAL.TheWorld.state.season
    local seasonal_bonuses = {
        spring = { frog = 1.5, flower = 1.3 },
        summer = { hound = 1.2, cactus = 1.4 },
        autumn = { spider = 1.1, berries = 1.2 },
        winter = { pengull = 1.5, walrus = 1.3 }
    }

    local season_data = seasonal_bonuses[season]
    if season_data then
        for pattern, bonus in pairs(season_data) do
            if prefab_name:find(pattern) then
                return bonus
            end
        end
    end

    return 1.0
end

-- 计算稀有度分数
function ContractTargets:GetRarityScore(prefab_name)
    local rarity_scores = {
        common = 1, spider = 1, hound = 2, berries = 1, grass = 1,
        uncommon = 3, tentacle = 3, tallbird = 3, gold = 3,
        rare = 5, deerclops = 8, gears = 6, thulecite = 7,
        legendary = 10
    }

    for pattern, score in pairs(rarity_scores) do
        if pattern ~= "common" and pattern ~= "uncommon" and
           pattern ~= "rare" and pattern ~= "legendary" and
           prefab_name:find(pattern) then
            return score
        end
    end

    return rarity_scores.common
end

-- 计算制作稀有度分数
function ContractTargets:GetCraftRarityScore(recipe_name, recipe_data)
    local base_score = self:GetRarityScore(recipe_name)

    -- 根据科技要求调整
    local tech_level = self:CalculateTechLevel(recipe_data)
    base_score = base_score + math.floor(tech_level / 2)

    return math.min(base_score, 10)
end

-- 计算科技等级
function ContractTargets:CalculateTechLevel(recipe_data)
    if not recipe_data or not recipe_data.level then return 0 end

    local total = 0
    for _, level in pairs(recipe_data.level) do
        total = total + (level or 0)
    end

    return total
end

-- 计算可达性分数
function ContractTargets:CalculateReachability(target)
    local score = 1.0

    -- 季节性调整
    score = score * target.seasonal_factor

    -- 稀有度惩罚
    if target.rarity > 5 then
        score = score * self.config.rarity_penalty
    end

    -- 模组内容权重
    if target.source and target.source:find("mod:") then
        score = score * self.config.mod_content_weight
    end

    return math.max(0, math.min(1, score))
end

-- 计算击杀目标权重
function ContractTargets:CalculateKillTargetWeight(target)
    local weight = 5 -- 基础权重

    -- 根据稀有度调整
    weight = weight + (target.rarity - 3)

    -- 根据季节性调整
    if target.seasonal_factor > 1.2 then
        weight = weight - 1 -- 当季容易的目标降低难度
    end

    return math.max(1, math.min(10, weight))
end

-- 计算采集目标权重
function ContractTargets:CalculateCollectTargetWeight(target)
    local weight = 4 -- 基础权重

    -- 根据稀有度调整
    weight = weight + (target.rarity - 2)

    return math.max(1, math.min(10, weight))
end

-- 计算制作目标权重
function ContractTargets:CalculateCraftTargetWeight(target)
    local weight = 5 -- 基础权重

    -- 根据科技等级调整
    if target.tech_level then
        weight = weight + math.floor(target.tech_level / 2)
    end

    return math.max(1, math.min(10, weight))
end

-- 计算制作可达性
function ContractTargets:CalculateCraftReachability(target)
    local score = self:CalculateReachability(target)

    -- 根据科技等级调整
    if target.tech_level and target.tech_level > 6 then
        score = score * 0.7 -- 高科技配方降低可达性
    end

    return score
end

-- 添加静态击杀目标（确保基础内容）
function ContractTargets:AddStaticKillTargets(targets)
    local static_targets = {
        {prefab = "spider", name = "蜘蛛", base_goal = 10, base_favor = 5, rarity = 1},
        {prefab = "hound", name = "猎犬", base_goal = 5, base_favor = 6, rarity = 2},
        {prefab = "tentacle", name = "触手", base_goal = 3, base_favor = 8, rarity = 3},
        {prefab = "tallbird", name = "高鸟", base_goal = 2, base_favor = 7, rarity = 3}
    }

    for _, static_target in ipairs(static_targets) do
        -- 检查是否已存在
        local exists = false
        for _, existing in ipairs(targets) do
            if existing.prefab == static_target.prefab then
                exists = true
                break
            end
        end

        if not exists then
            static_target.type = "kill"
            static_target.base_rep = {pig = 2}
            static_target.source = "base"
            static_target.seasonal_factor = self:GetSeasonalFactor(static_target.prefab)
            static_target.weight = self:CalculateKillTargetWeight(static_target)
            static_target.reachability = self:CalculateReachability(static_target)
            table.insert(targets, static_target)
        end
    end
end

-- 添加静态采集目标
function ContractTargets:AddStaticCollectTargets(targets)
    local static_targets = {
        {item = "berries", name = "浆果", base_goal = 30, base_favor = 4, rarity = 1},
        {item = "carrot", name = "胡萝卜", base_goal = 20, base_favor = 5, rarity = 1},
        {item = "flint", name = "燧石", base_goal = 15, base_favor = 6, rarity = 2},
        {item = "twigs", name = "树枝", base_goal = 40, base_favor = 3, rarity = 1}
    }

    for _, static_target in ipairs(static_targets) do
        local exists = false
        for _, existing in ipairs(targets) do
            if existing.item == static_target.item then
                exists = true
                break
            end
        end

        if not exists then
            static_target.type = "collect"
            static_target.base_rep = {pig = 1}
            static_target.source = "base"
            static_target.seasonal_factor = self:GetSeasonalFactor(static_target.item)
            static_target.weight = self:CalculateCollectTargetWeight(static_target)
            static_target.reachability = self:CalculateReachability(static_target)
            table.insert(targets, static_target)
        end
    end
end

-- 添加静态制作目标
function ContractTargets:AddStaticCraftTargets(targets)
    local static_targets = {
        {item = "axe", name = "斧头", base_goal = 3, base_favor = 8, tech_level = 1},
        {item = "pickaxe", name = "镐子", base_goal = 3, base_favor = 8, tech_level = 1},
        {item = "spear", name = "长矛", base_goal = 5, base_favor = 6, tech_level = 1}
    }

    for _, static_target in ipairs(static_targets) do
        local exists = false
        for _, existing in ipairs(targets) do
            if existing.item == static_target.item then
                exists = true
                break
            end
        end

        if not exists then
            static_target.type = "craft"
            static_target.base_rep = {pig = 2}
            static_target.source = "base"
            static_target.seasonal_factor = 1.0
            static_target.rarity = 2
            static_target.weight = self:CalculateCraftTargetWeight(static_target)
            static_target.reachability = self:CalculateCraftReachability(static_target)
            table.insert(targets, static_target)
        end
    end
end

return ContractTargets
