-- Alternative approach: Hook into chat system directly
-- 确保能访问全局变量
local GLOBAL = rawget(_G, "GLOBAL") or _G
local STRINGS = GLOBAL.STRINGS
local TheNet = GLOBAL.TheNet

local function IsMaster() return TheNet:GetIsServer() end

local function Broadcast(msg)
    if TheNet:GetIsServer() then
        TheNet:Announce("[商旅巡游录] " .. tostring(msg))
    end
end

-- Command registry
local commands = {}

local function RegisterCommand(name, desc, fn)
    commands[name] = {
        desc = desc,
        fn = fn
    }
    print("[商旅巡游录] Registered command:", name)
end

-- Simple trim function
local function trim(s)
    return s:match("^%s*(.-)%s*$")
end

-- Hook into player chat to catch commands (without slash)
local function OnPlayerSay(player, message)
    if not player or not message then return end

    -- Check if message is exactly a command (no slash needed)
    local cmd = string.lower(trim(message))
    print("[商旅巡游录] Chat message detected:", cmd, "from", player.userid or "unknown")

    -- 不处理单个字母'v'，避免与正常聊天冲突
    -- 只处理明确的命令
    if commands[cmd] then
        print("[商旅巡游录] Executing command:", cmd)
        commands[cmd].fn(player)
        return true -- Consume the message
    end
    return false
end

-- Hook into the chat system
local function HookChatSystem()
    -- Hook into player talker component
    AddPlayerPostInit(function(player)
        if not player.components.talker then return end

        local old_say = player.components.talker.Say
        player.components.talker.OldSay = old_say  -- Save reference for commands
        player.components.talker.Say = function(self, message, ...)
            if OnPlayerSay(player, message) then
                -- Command was handled, don't broadcast the chat
                return
            end
            -- Normal chat, proceed
            return old_say(self, message, ...)
        end
    end)
end

-- Initialize the chat hook system
HookChatSystem()

-- Register commands with mod prefix to avoid conflicts
RegisterCommand("chelp", "显示商旅巡游录帮助", function(player)
    print("[商旅巡游录] Help command executed for player:", player and player.userid or "nil")
    if player and player.components and player.components.talker then
        -- Use the original Say function to avoid recursion
        local talker = player.components.talker
        if talker.OldSay then
            talker:OldSay(STRINGS.CARAVAN.HELP)
        else
            Broadcast(STRINGS.CARAVAN.HELP)
        end
    else
        Broadcast(STRINGS.CARAVAN.HELP)
    end
end)

RegisterCommand("cmutators", "查看今日词条", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdShowMutators(player) end
end)

RegisterCommand("creroll", "发起重掷词条投票", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdRerollMutators(player) end
end)

RegisterCommand("ccontracts", "查看当前合约", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdShowContracts(player) end
end)

-- 处理带参数的交付命令
local function HandleDeliverCommand(player, message)
    local cmd_parts = {}
    for part in string.gmatch(message, "%S+") do
        table.insert(cmd_parts, part)
    end

    if #cmd_parts >= 2 and string.lower(cmd_parts[1]) == "cdeliver" then
        local contract_id = cmd_parts[2]
        local w = GLOBAL.TheWorld
        local comp = w and w.components and w.components.modworld_global
        if comp then
            comp:CmdDeliverContract(player, contract_id)
            return true
        end
    end
    return false
end

-- 处理被动恩惠相关的带参数命令
local function HandleBoonsCommand(player, message)
    local cmd_parts = {}
    for part in string.gmatch(message, "%S+") do
        table.insert(cmd_parts, part)
    end

    if #cmd_parts >= 2 then
        local cmd = string.lower(cmd_parts[1])
        local action = string.lower(cmd_parts[2])

        if cmd == "cboon" then
            local boon_comp = player and player.components and player.components.modplayer_boons
            if not boon_comp then
                if player.components.talker then
                    player.components.talker:Say("被动恩惠系统错误")
                end
                return true
            end

            if action == "list" then
                -- 显示所有可用的被动恩惠
                local available = boon_comp:GetAvailableBoons()
                local parts = {"可用被动恩惠："}

                for boon_id, boon_def in pairs(available) do
                    local unlocked = boon_comp.unlocked_boons[boon_id] or 0
                    local equipped = false
                    for _, equipped_id in ipairs(boon_comp.equipped_boons) do
                        if equipped_id == boon_id then
                            equipped = true
                            break
                        end
                    end

                    local status = ""
                    if equipped then
                        status = "[已装备]"
                    elseif unlocked > 0 then
                        status = "[已解锁]"
                    else
                        status = string.format("[%d恩惠]", boon_def.cost)
                    end

                    table.insert(parts, string.format("%s %s：%s", status, boon_def.name, boon_def.desc))
                end

                local msg = table.concat(parts, " ")
                if player.components.talker then
                    player.components.talker:Say(msg)
                end
                return true

            elseif action == "unlock" and #cmd_parts >= 3 then
                -- 解锁被动恩惠（统一走RPC）
                local boon_id = cmd_parts[3]
                GLOBAL.SendModRPCToServer(GLOBAL.GetModRPC("thinking","boons_action"), "unlock", boon_id)
                return true

            elseif action == "equip" and #cmd_parts >= 3 then
                -- 装备被动恩惠（统一走RPC）
                local boon_id = cmd_parts[3]
                GLOBAL.SendModRPCToServer(GLOBAL.GetModRPC("thinking","boons_action"), "equip", boon_id)
                return true

            elseif action == "unequip" and #cmd_parts >= 3 then
                -- 卸下被动恩惠（统一走RPC）
                local boon_id = cmd_parts[3]
                GLOBAL.SendModRPCToServer(GLOBAL.GetModRPC("thinking","boons_action"), "unequip", boon_id)
                return true

            else
                if player.components.talker then
                    player.components.talker:Say("用法: cboon list/unlock/equip/unequip <被动ID>")
                end
                return true
            end
        end
    end

    return false
end

-- 修改聊天钩子以处理带参数的命令
local old_OnPlayerSay = OnPlayerSay
OnPlayerSay = function(player, message)
    if not player or not message then return end

    -- 首先尝试处理带参数的命令
    if HandleDeliverCommand(player, message) then
        return true
    end

    -- 处理被动恩惠命令
    if HandleBoonsCommand(player, message) then
        return true
    end

    -- 然后处理原有的简单命令
    return old_OnPlayerSay(player, message)
end

RegisterCommand("cboons", "查看被动恩惠", function(player)
    if player then
        GLOBAL.SendModRPCToServer(GLOBAL.GetModRPC("thinking","boons_request"))
    end
end)



RegisterCommand("crep", "查看阵营声望", function(player)
    if player and player.components and player.components.modplayer_rep then
        player.components.modplayer_rep:CmdShowRep()
    end
end)

-- 献祭状态
RegisterCommand("sacrifice", "查看献祭状态", function(player)
    if not player then return end
    SendModRPCToServer(GetModRPC("thinking","sacrifice_request"))
end)

-- 简单 spin 命令：spin blood|soul|hunger|item
RegisterCommand("spin", "进行献祭：spin <blood|soul|hunger|item>", function(player)
    if not player then return end
    -- 解析参数
    -- commands系统当前只能匹配完整词；添加带参数版钩子
    if player.components.talker then
        player.components.talker:Say("用法: spin <blood|soul|hunger|item> 或 spin confirm <type> <token>")
    end
end)

-- 带参数处理（扩展 HandleBoonsCommand 风格）
local function HandleSacrificeCommand(player, message)
    local parts = {}
    for part in string.gmatch(message, "%S+") do table.insert(parts, part) end
    if #parts == 0 then return false end

    local cmd = string.lower(parts[1])
    if cmd ~= "spin" and cmd ~= "rarity" then return false end

    if cmd == "spin" then
        if #parts >= 4 and parts[2] == "confirm" then
            local t = parts[3]
            local token = parts[4]
            SendModRPCToServer(GetModRPC("thinking","sacrifice_action"), "confirm", t, token)
            return true
        elseif #parts >= 2 then
            local t = parts[2]
            SendModRPCToServer(GetModRPC("thinking","sacrifice_action"), "spin", t)
            return true
        end
        return false
    elseif cmd == "rarity" then
        if #parts >= 2 then
            local subcmd = parts[2]
            if subcmd == "scan" then
                SendModRPCToServer(GetModRPC("thinking","rarity_scan"))
                return true
            elseif subcmd == "debug" then
                SendModRPCToServer(GetModRPC("thinking","rarity_debug"))
                return true
            end
        end
        -- 默认显示献祭状态
        SendModRPCToServer(GetModRPC("thinking","sacrifice_request"))
        return true
    end

    return false
end

-- 注入带参数命令处理
local old_OnPlayerSay2 = OnPlayerSay
OnPlayerSay = function(player, message)
    if HandleSacrificeCommand(player, message) then return true end
    return old_OnPlayerSay2(player, message)
end

RegisterCommand("cteleport", "闪现术(需要装备闪现术被动恩惠)", function(player)
    if player then
        local pos = GLOBAL.TheInput and GLOBAL.TheInput:GetWorldPosition() or nil
        if pos then
            GLOBAL.SendModRPCToServer(GLOBAL.GetModRPC("thinking","boons_action"), "teleport", tostring(pos.x), tostring(pos.z))
        end
    end
end)

RegisterCommand("ccaravan", "查看商队信息", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdShowCaravan(player) end
end)

RegisterCommand("cui", "打开商旅巡游录UI界面", function(player)
    if player then
        -- Use the same toggle function as the V key
        if GLOBAL.ToggleCaravanUI then
            GLOBAL.ToggleCaravanUI()
        else
            -- Fallback method
            local CaravanScreen = GLOBAL.require("screens/caravanscreen")
            local screen = CaravanScreen(player)
            GLOBAL.TheFrontEnd:PushScreen(screen)
        end

        if player.components and player.components.talker then
            player.components.talker:Say("商旅巡游录界面")
        end
    end
end)

RegisterCommand("cfavor", "添加星尘代币(测试用)", function(player, amount)
    if player and player.components and player.components.modplayer_boons then
        local add_amount = tonumber(amount) or 50
        player.components.modplayer_boons:AddFavor(add_amount)
    end
end)

RegisterCommand("cboonshelp", "显示被动恩惠系统完整帮助", function(player)
    if not player or not player.components or not player.components.talker then return end

    local help_messages = {
        "=== 被动恩惠系统帮助 ===",
        "",
        "UI操作方式:",
        "• 使用 'cui' 命令或按V键打开界面",
        "• 切换到'被动恩惠'标签页",
        "• 鼠标悬停在按钮上查看详细信息",
        "• 点击按钮进行解锁/装备/卸下操作",
        "",
        "状态说明:",
        "• 深灰色按钮 = 未解锁状态",
        "• 黄色按钮 = 已解锁但未装备",
        "• 绿色按钮 = 已装备状态",
        "",
        "职业分类:",
        "• 战士系: 物理输出强化",
        "• 法师系: 魔法技能增强",
        "• 召唤师系: 召唤生物协战",
        "• 农民系: 生产采集加成",
        "",
        "洗点功能:",
        "• 点击'洗点重置'按钮",
        "• 费用 = 已装备数量 × 10恩惠",
        "• 重置所有解锁状态并返还费用"
    }

    for _, msg in ipairs(help_messages) do
        player.components.talker:Say(msg)
        -- 添加小延迟避免消息过快
        player:DoTaskInTime(0.1 * _, function() end)
    end
end)

-- /caravan
RegisterCommand("caravan", "查看商队信息", function(player)
    local w = GLOBAL.TheWorld
    local comp = w and w.components and w.components.modworld_global
    if comp then comp:CmdShowCaravan(player) end
end)

-- 调试命令：重新应用所有词条效果
RegisterCommand("creapply", "重新应用词条效果到所有玩家", function(player)
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        world_comp:ApplyMutatorEffectsToAllPlayers()
        if player and player.components and player.components.talker then
            player.components.talker:Say("已重新应用词条效果到所有玩家")
        end
    else
        if player and player.components and player.components.talker then
            player.components.talker:Say("世界组件未初始化")
        end
    end
end)

-- 调试命令：检查词条系统状态
RegisterCommand("cstatus", "检查词条系统状态", function(player)
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        local current_day = (GLOBAL.TheWorld and GLOBAL.TheWorld.state and GLOBAL.TheWorld.state.cycles) or 0
        local mutator_count = #(world_comp.mutators or {})
        local player_count = #(GLOBAL.AllPlayers or {})

        local status = string.format("词条系统状态: 当前第%d天, %d个词条, %d个玩家在线",
                                   current_day, mutator_count, player_count)

        if player and player.components and player.components.talker then
            player.components.talker:Say(status)

            -- 显示当前词条
            if mutator_count > 0 then
                for i, mutator in ipairs(world_comp.mutators) do
                    local type_icon = ""
                    if mutator.type == "positive" then type_icon = "✓"
                    elseif mutator.type == "negative" then type_icon = "✗"
                    elseif mutator.type == "neutral" then type_icon = "◈"
                    elseif mutator.type == "event" then type_icon = "★"
                    end
                    player.components.talker:Say(string.format("[%d]%s%s", i, type_icon, mutator.desc))
                end
            end

            -- 显示玩家重投状态
            local reroll_comp = player.components.modplayer_reroll
            if reroll_comp then
                local can_reroll = reroll_comp:CanReroll() and "可以" or "不能"
                player.components.talker:Say(string.format("重投状态: %s重投", can_reroll))
            end
        end
    else
        if player and player.components and player.components.talker then
            player.components.talker:Say("世界组件未初始化")
        end
    end
end)

-- 添加全局控制台命令 c_rerolll (用于测试)
-- 这个命令绕过重投限制，直接重掷词条，仅用于测试目的
local function c_rerolll()
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        print("[商旅巡游录] c_rerolll: 强制重掷词条 (测试命令)")
        world_comp:RollMutators()
        world_comp:AnnounceMutators()
        print("[商旅巡游录] c_rerolll: 重掷完成")
        return "已强制重掷词条！(测试命令)"
    else
        print("[商旅巡游录] c_rerolll: 世界组件未初始化")
        return "世界组件未初始化"
    end
end

-- 将命令注册到全局环境，这样就可以在控制台使用 c_rerolll()
GLOBAL.rawset(GLOBAL, "c_rerolll", c_rerolll)

-- === 合约系统控制台测试命令（c_xxxx）===
-- 参考 c_rerolll 的实现，提供控制台可直接调用的测试函数
local function c_contracts()
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        print("[商旅巡游录] c_contracts: 打印当前合约")
        world_comp:CmdShowContracts(GLOBAL.ThePlayer)
        return "已打印当前合约（聊天/控制台）"
    else
        print("[商旅巡游录] c_contracts: 世界组件未初始化")
        return "世界组件未初始化"
    end
end
GLOBAL.rawset(GLOBAL, "c_contracts", c_contracts)

local function c_deliver(id)
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        print(string.format("[商旅巡游录] c_deliver: 尝试交付合约 %s", tostring(id)))
        world_comp:CmdDeliverContract(GLOBAL.ThePlayer, tostring(id))
        return string.format("已尝试交付合约 %s", tostring(id))
    else
        print("[商旅巡游录] c_deliver: 世界组件未初始化")
        return "世界组件未初始化"
    end
end
GLOBAL.rawset(GLOBAL, "c_deliver", c_deliver)

local function c_contracts_gen()
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        print("[商旅巡游录] c_contracts_gen: 重新生成全部合约")
        world_comp:GenerateContracts()
        world_comp:SyncContractProgress()
        return "已重新生成全部合约"
    else
        print("[商旅巡游录] c_contracts_gen: 世界组件未初始化")
        return "世界组件未初始化"
    end
end
GLOBAL.rawset(GLOBAL, "c_contracts_gen", c_contracts_gen)

local function c_contracts_refresh_one(index)
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        local ok, msg = world_comp:RefreshSingleContract(tonumber(index))
        if ok then
            print(string.format("[商旅巡游录] c_contracts_refresh_one: 已刷新第 %d 条合约", tonumber(index) or -1))
            world_comp:SyncContractProgress()
            return "已刷新指定合约"
        else
            print("[商旅巡游录] c_contracts_refresh_one: 刷新失败", msg or "")
            return "刷新失败"
        end
    else
        print("[商旅巡游录] c_contracts_refresh_one: 世界组件未初始化")
        return "世界组件未初始化"
    end
end
GLOBAL.rawset(GLOBAL, "c_contracts_refresh_one", c_contracts_refresh_one)

local function c_contracts_snapshot()
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        local s = world_comp:GetContractsSnapshotForClient(GLOBAL.ThePlayer)
        if type(s) == "table" then
            print("[商旅巡游录] c_contracts_snapshot:")
            for k,v in pairs(s) do
                if type(v) ~= "table" then
                    print("  ", k, v)
                end
            end
            if s.contracts then
                print(string.format("  contracts: %d 条", #s.contracts))
                for i, c in ipairs(s.contracts) do
                    print(string.format("    [%d] type=%s id=%s progress=%d/%d completed=%s",
                        i, tostring(c.type), tostring(c.id), tonumber(c.progress or 0), tonumber(c.goal or 0), tostring(c.completed)))
                end
            end
            return "已打印合约快照"
        else
            print(tostring(s))
            return "已输出快照"
        end
    else
        print("[商旅巡游录] c_contracts_snapshot: 世界组件未初始化")
        return "世界组件未初始化"
    end
end
GLOBAL.rawset(GLOBAL, "c_contracts_snapshot", c_contracts_snapshot)


-- 添加其他有用的测试命令
local function c_mutators()
    local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
    if world_comp then
        local parts = {"今日词条："}
        for i, m in ipairs(world_comp.mutators) do
            local type_icon = ""
            if m.type == "positive" then type_icon = "✓"
            elseif m.type == "negative" then type_icon = "✗"
            elseif m.type == "neutral" then type_icon = "◈"
            elseif m.type == "event" then type_icon = "★"
            end
            table.insert(parts, string.format("[%d]%s%s", i, type_icon, m.desc))
        end
        local msg = table.concat(parts, " ")
        print(msg)
        return msg
    else
        print("世界组件未初始化")
        return "世界组件未初始化"
    end
end

local function c_findmutator(target_name)
    if not target_name then
        print("用法: c_findmutator(\"词条名\")")
        return "用法: c_findmutator(\"词条名\")"
    end

    local attempts = 0
    local max_attempts = 50

    while attempts < max_attempts do
        local world_comp = GLOBAL.TheWorld and GLOBAL.TheWorld.components and GLOBAL.TheWorld.components.modworld_global
        if not world_comp then
            print("世界组件未初始化")
            return "世界组件未初始化"
        end

        local found = false
        for _, mutator in ipairs(world_comp.mutators) do
            if string.find(string.lower(mutator.desc), string.lower(target_name)) or
               string.find(string.lower(mutator.id), string.lower(target_name)) then
                found = true
                print(string.format("✅ 找到目标词条！尝试次数: %d", attempts + 1))
                c_mutators()
                return string.format("找到目标词条！尝试次数: %d", attempts + 1)
            end
        end

        if not found then
            attempts = attempts + 1
            if attempts % 10 == 0 then
                print(string.format("已尝试 %d 次...", attempts))
            end
            c_rerolll()
        end
    end

    print(string.format("❌ 未能在 %d 次尝试内找到目标词条", max_attempts))
    return string.format("未能在 %d 次尝试内找到目标词条", max_attempts)
end

-- 注册额外的测试命令
GLOBAL.rawset(GLOBAL, "c_mutators", c_mutators)
GLOBAL.rawset(GLOBAL, "c_findmutator", c_findmutator)