-- Component files also need GLOBAL declaration in DST when accessing global variables
-- luacheck: globals PickRandomHostilePrefab

-- 确保能访问全局变量
local GLOBAL = rawget(_G, "GLOBAL") or _G

local DEGREES = GLOBAL.DEGREES or (math.pi / 180)
local GROUND = GLOBAL.GROUND or {}
local GetTime = GLOBAL.GetTime or function() return 0 end
local SpawnPrefab = GLOBAL.SpawnPrefab or function() return nil end
local TheWorld = GLOBAL.TheWorld or {Map = {GetTileAtPoint = function() return 1 end}}

-- 被动恩惠定义 - 按职业分类
local BOON_DEFINITIONS = {
    -- === 战士系 ===
    warrior_speed = {
        name = "战士疾行",
        desc = "移动速度提升20%",
        cost = 25,
        max_level = 1,
        category = "warrior",
        effect_type = "speed",
        effect_value = 1.2
    },
    warrior_damage = {
        name = "战士之力",
        desc = "攻击力提升30%",
        cost = 35,
        max_level = 1,
        category = "warrior",
        effect_type = "damage_boost",
        effect_value = 1.3
    },
    warrior_combo = {
        name = "连击专精",
        desc = "连续攻击同一目标时伤害递增(最多5层)",
        cost = 50,
        max_level = 1,
        category = "warrior",
        effect_type = "combo_system",
        effect_value = 5
    },

    -- === 法师系 ===
    mage_poison = {
        name = "毒素掌控",
        desc = "攻击附带毒伤，每秒造成10点伤害，持续5秒",
        cost = 40,
        max_level = 1,
        category = "mage",
        effect_type = "poison_damage",
        effect_value = {damage = 10, duration = 5}
    },
    mage_charge = {
        name = "蓄力打击",
        desc = "长按攻击键蓄力，最多增加200%伤害",
        cost = 60,
        max_level = 1,
        category = "mage",
        effect_type = "charge_attack",
        effect_value = 3.0
    },
    mage_teleport = {
        name = "闪现术",
        desc = "双击移动键瞬移8格距离(冷却30秒)",
        cost = 80,
        max_level = 1,
        category = "mage",
        effect_type = "teleport_skill",
        effect_value = {distance = 8, cooldown = 30}
    },

    -- === 召唤师系 ===
    summon_spider = {
        name = "蜘蛛召唤",
        desc = "召唤友好蜘蛛协助战斗(最多2只)",
        cost = 45,
        max_level = 1,
        category = "summoner",
        effect_type = "summon_creature",
        effect_value = {prefab = "spider_warrior", max_count = 2, duration = 120}
    },
    -- 第二个改为原第三个：巨兽召唤
    summon_boss = {
        name = "巨兽召唤",
        desc = "召唤小型巨鹿协助战斗(最多1只)",
        cost = 120,
        max_level = 1,
        category = "summoner",
        effect_type = "summon_creature",
        effect_value = {prefab = "deerclops", max_count = 1, duration = 60, scale = 0.5}
    },
    -- 第三个：随机敌对生物召唤（含其他模组敌怪）
    summon_random = {
        name = "未知契约",
        desc = "随机召唤一名敌对生物作为随从（包含其他模组）。Boss属性减半并上限5000生命。复活时重新抽取。",
        cost = 90,
        max_level = 1,
        category = "summoner",
        effect_type = "summon_creature",
        effect_value = {random = true, max_count = 1, duration = 120}
    },

    -- === 农民系 ===
    farmer_growth = {
        name = "绿拇指",
        desc = "玩家周围8格范围内植物生长速度加快100%",
        cost = 30,
        max_level = 1,
        category = "farmer",
        effect_type = "plant_growth",
        effect_value = {range = 8, growth_multiplier = 2.0, check_interval = 10}
    },
    farmer_harvest = {
        name = "自动收获",
        desc = "玩家周围1格范围内成熟作物自动收获",
        cost = 55,
        max_level = 1,
        category = "farmer",
        effect_type = "auto_harvest",
        effect_value = {range = 1, check_interval = 5}
    },
    farmer_blessing = {
        name = "丰收祝福",
        desc = "采集时恢复饥饿+10、理智+5、生命+3",
        cost = 75,
        max_level = 1,
        category = "farmer",
        effect_type = "harvest_blessing",
        effect_value = {hunger = 10, sanity = 5, health = 3}
    }
}

local ModPlayerBoons = Class(function(self, inst)
    self.inst = inst
    self.favor = 0
    self.unlocked_boons = {}  -- {boon_id = level}
    self.equipped_boons = {}  -- [slot1, slot2] 最多装备2个
    self.max_equipped = 2
    self.active_effects = {}  -- 当前激活的效果清理函数

    -- 战士系统相关
    self.combo_stacks = {}  -- {target_guid = stack_count}
    self.combo_timers = {}  -- {target_guid = timer_task}

    -- 法师系统相关
    self.charge_start_time = 0
    self.is_charging = false
    self.teleport_cooldown = 0
    self.last_move_time = 0
    self.move_key_count = 0

    -- 召唤师系统相关
    self.summoned_creatures = {}  -- {boon_id = {creature1, creature2, ...}}
    self.summon_respawn_tasks = {} -- {boon_id = {task1, task2, ...}}
end)


-- 在世界已加载的实体中选择一个敌对生物的prefab（兼容其他模组）
function ModPlayerBoons:PickRandomHostilePrefab()
    local candidates, seen = {}, {}
    if GLOBAL and GLOBAL.Ents then
        for _, e in pairs(GLOBAL.Ents) do
            if e and e.prefab and not seen[e.prefab] then
                if (e:HasTag("hostile") or e:HasTag("monster") or e:HasTag("epic"))
                    and not e:HasTag("player") and not e:HasTag("companion") and not e:HasTag("wall") then
                    table.insert(candidates, e.prefab)
                    seen[e.prefab] = true
                end
            end
        end
    end
    if #candidates == 0 then
        candidates = {"spider_warrior", "hound", "tentacle", "merm", "bishop", "rook", "leif", "spat"}
    end
    return candidates[math.random(#candidates)]
end

function ModPlayerBoons:AddFavor(amount)
    self.favor = self.favor + amount
    print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "获得", amount, "恩惠之力，总计:", self.favor)

    if self.inst.components.talker then
        self.inst.components.talker:Say(string.format("获得 %d 恩惠之力！(总计: %d)", amount, self.favor))
    end
end

-- 获取被动恩惠定义
function ModPlayerBoons:GetBoonDefinition(boon_id)
    return BOON_DEFINITIONS[boon_id]
end

-- 获取所有可用的被动恩惠
function ModPlayerBoons:GetAvailableBoons()
    return BOON_DEFINITIONS
end

-- 检查是否可以购买被动恩惠
function ModPlayerBoons:CanUnlockBoon(boon_id)
    local boon_def = BOON_DEFINITIONS[boon_id]
    if not boon_def then return false, "未知的被动恩惠" end

    local current_level = self.unlocked_boons[boon_id] or 0
    if current_level >= boon_def.max_level then
        return false, "已达到最大等级"
    end

    if self.favor < boon_def.cost then
        return false, string.format("恩惠之力不足，需要%d", boon_def.cost)
    end

    return true, "可以购买"
end

-- 购买/解锁被动恩惠
function ModPlayerBoons:UnlockBoon(boon_id, silent)
    local can_unlock, reason = self:CanUnlockBoon(boon_id)
    if not can_unlock then
        if not silent and self.inst.components.talker then
            self.inst.components.talker:Say(reason)
        end
        return false
    end

    local boon_def = BOON_DEFINITIONS[boon_id]
    self.favor = self.favor - boon_def.cost
    self.unlocked_boons[boon_id] = (self.unlocked_boons[boon_id] or 0) + 1

    if not silent and self.inst.components.talker then
        self.inst.components.talker:Say(string.format("✓ 解锁了 %s！", boon_def.name))
    end

    print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "解锁被动恩惠:", boon_def.name)
    return true
end

-- 检查是否可以装备被动恩惠
function ModPlayerBoons:CanEquipBoon(boon_id)
    if not self.unlocked_boons[boon_id] then
        return false, "尚未解锁此被动恩惠"
    end

    -- 检查是否已经装备
    for _, equipped_id in ipairs(self.equipped_boons) do
        if equipped_id == boon_id then
            return false, "已经装备此被动恩惠"
        end
    end

    if #self.equipped_boons >= self.max_equipped then
        return false, string.format("装备槽已满（%d/%d）", #self.equipped_boons, self.max_equipped)
    end

    return true, "可以装备"
end

-- 装备被动恩惠
function ModPlayerBoons:EquipBoon(boon_id, silent)
    local can_equip, reason = self:CanEquipBoon(boon_id)
    if not can_equip then
        if not silent and self.inst.components.talker then
            self.inst.components.talker:Say(reason)
        end
        return false
    end

    table.insert(self.equipped_boons, boon_id)
    self:ApplyBoonEffect(boon_id)

    local boon_def = BOON_DEFINITIONS[boon_id]
    if not silent and self.inst.components.talker then
        self.inst.components.talker:Say(string.format("✓ 装备了 %s", boon_def.name))
    end

    print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "装备被动恩惠:", boon_def.name)
    return true
end

-- 卸下被动恩惠
function ModPlayerBoons:UnequipBoon(boon_id, silent)
    for i, equipped_id in ipairs(self.equipped_boons) do
        if equipped_id == boon_id then
            table.remove(self.equipped_boons, i)
            self:RemoveBoonEffect(boon_id)

            local boon_def = BOON_DEFINITIONS[boon_id]
            if not silent and self.inst.components.talker then
                self.inst.components.talker:Say(string.format("✓ 卸下了 %s", boon_def.name))
            end

            print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "卸下被动恩惠:", boon_def.name)
            return true
        end
    end

    if not silent and self.inst.components.talker then
        self.inst.components.talker:Say("未装备此被动恩惠")
    end
    return false
end

function ModPlayerBoons:CmdShowBoons()
    local msg = string.format("Favor:%d 已解锁:%d 已装备:%d/%d",
        self.favor,
        self:GetUnlockedCount(),
        #self.equipped_boons,
        self.max_equipped)
    if self.inst.components.talker then
        self.inst.components.talker:Say(msg)
    end
end

-- 获取已解锁的被动恩惠数量
function ModPlayerBoons:GetUnlockedCount()
    local count = 0
    for _ in pairs(self.unlocked_boons) do
        count = count + 1
    end
    return count
end

-- 洗点功能 - 重置所有被动恩惠（不返还点数）
function ModPlayerBoons:RespecAllBoons(cost)
    if not cost then
        cost = #self.equipped_boons * 10  -- 默认费用计算
    end

    if self.favor < cost then
        if self.inst.components.talker then
            self.inst.components.talker:Say(string.format("洗点需要%d恩惠之力，当前只有%d", cost, self.favor))
        end
        return false
    end

    -- 卸下所有装备的被动恩惠（静默卸下，不显示消息）
    local equipped_copy = {}
    for _, boon_id in ipairs(self.equipped_boons) do
        table.insert(equipped_copy, boon_id)
    end

    for _, boon_id in ipairs(equipped_copy) do
        self:UnequipBoon(boon_id, true)  -- 静默卸下
    end

    -- 扣除洗点费用（不返还解锁费用）
    self.favor = self.favor - cost

    print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "洗点完成，费用:", cost, "不返还解锁费用")
    return true
end

-- === 法师系支持函数 ===

-- 应用毒伤效果
function ModPlayerBoons:ApplyPoisonDamage(target, damage_per_tick, duration)
    if not target or not target:IsValid() or not target.components.health then return end

    -- 检查目标是否已经中毒
    if target.poison_task then
        target.poison_task:Cancel()
    end

    local ticks = 0
    local max_ticks = duration

    target.poison_task = target:DoPeriodicTask(1, function()
        ticks = ticks + 1
        if target and target:IsValid() and target.components.health and not target.components.health:IsDead() then
            target.components.health:DoDelta(-damage_per_tick, false, "poison")

            -- 毒伤特效
            if target.SoundEmitter then
                target.SoundEmitter:PlaySound("dontstarve/creatures/spider/hit")
            end
        end

        if ticks >= max_ticks then
            target.poison_task:Cancel()
            target.poison_task = nil
        end
    end)

    if self.inst.components.talker then
        self.inst.components.talker:Say("中毒了!")
    end
end

-- 执行传送
function ModPlayerBoons:PerformTeleport(distance)
    local player = self.inst
    if not player or not player:IsValid() then return end

    local x, _, z = player.Transform:GetWorldPosition()
    local angle = player.Transform:GetRotation() * GLOBAL.DEGREES

    -- 计算传送目标位置
    local target_x = x + math.cos(angle) * distance
    local target_z = z - math.sin(angle) * distance

    -- 检查目标位置是否安全
    local ground = GLOBAL.TheWorld.Map:GetTileAtPoint(target_x, 0, target_z)
    if ground == GLOBAL.GROUND.IMPASSABLE or ground == GLOBAL.GROUND.INVALID then
        if player.components.talker then
            player.components.talker:Say("无法传送到那里!")
        end
        return
    end

    -- 执行传送
    if player.Physics then
        player.Physics:Teleport(target_x, 0, target_z)
    else
        player.Transform:SetPosition(target_x, 0, target_z)
    end

    -- 传送特效
    if player.SoundEmitter then
        player.SoundEmitter:PlaySound("dontstarve/common/teleportworm/teleport")
    end

    if player.components.talker then
        player.components.talker:Say("闪现!")
    end
end





-- === 召唤师系支持函数 ===

-- 召唤生物（支持随机敌对单位）
function ModPlayerBoons:SummonCreature(boon_id, summon_data)
    local player = self.inst
    if not player or not player:IsValid() then return end

    -- 初始化召唤列表
    self.summoned_creatures[boon_id] = self.summoned_creatures[boon_id] or {}

    -- 清理已死亡的召唤物
    for i = #self.summoned_creatures[boon_id], 1, -1 do
        local c = self.summoned_creatures[boon_id][i]
        if not c or not c:IsValid() or (c.components.health and c.components.health:IsDead()) then
            table.remove(self.summoned_creatures[boon_id], i)
        end
    end

    -- 数量限制
    if #self.summoned_creatures[boon_id] >= (summon_data.max_count or 1) then
        if player.components.talker then player.components.talker:Say("召唤数量已达上限!") end
        return
    end

    -- 选择prefab
    local chosen_prefab = summon_data.prefab
    local is_random = summon_data.random == true
    if is_random then
        chosen_prefab = self:PickRandomHostilePrefab()
    end

    -- 生成位置
    local x, _, z = player.Transform:GetWorldPosition()
    local ang = math.random() * 2 * math.pi
    local r = 2
    local sx = x + math.cos(ang) * r
    local sz = z + math.sin(ang) * r

    local creature = GLOBAL.SpawnPrefab(chosen_prefab)
    if not creature then
        print("[商旅巡游录] 召唤失败，无法生成:", tostring(chosen_prefab))
        return
    end
    creature.Transform:SetPosition(sx, 0, sz)

    -- 设为友好随从
    creature:AddTag("companion")
    creature:AddTag("summoned")
    if creature.components.combat then
        creature.components.combat:SetTarget(nil)
    end
    if creature:HasTag("monster") then creature:RemoveTag("monster") end
    if creature.components.follower then
        creature.components.follower:SetLeader(player)
    end

    -- Boss判定与削弱
    local is_boss = creature:HasTag("epic") or creature:HasTag("largecreature")
    if is_boss and creature.components.health then
        local maxh = creature.components.health.maxhealth or 0
        local newmax = math.min(math.floor(maxh * 0.5), 5000)
        if newmax > 0 then
            creature.components.health:SetMaxHealth(newmax)
            creature.components.health:SetPercent(1)
        end
    end
    if is_boss and creature.components.combat and creature.components.combat.defaultdamage then
        creature.components.combat:SetDefaultDamage(creature.components.combat.defaultdamage * 0.5)
    end

    -- 生存时间（可选）
    if (summon_data.duration or 0) > 0 then
        creature:DoTaskInTime(summon_data.duration, function()
            if creature and creature:IsValid() then creature:Remove() end
        end)
    end

    -- 加入列表并跟踪死亡复活（随机型在复活时重新抽取）
    table.insert(self.summoned_creatures[boon_id], creature)
    self:_TrackSummonedCreature(boon_id, creature, is_random)

    -- 台词：第二被动（巨兽召唤）与第三被动（未知契约）分开语句池
    if player.components.talker then
        if boon_id == "summon_boss" then
            -- 第二被动：仅使用“调侃语句池A”
            local phrases_b = {
                "你先顶上，我在后面看着。",
                "别慌，跟紧我就行。",
                "小心点，别被打倒了。",
                "看起来不太聪明的样子……正合适。",
                "站我后面，别送命。",
            }
            player.components.talker:Say(phrases_b[math.random(#phrases_b)])
        elseif is_random then
            -- 第三被动：Boss 固定台词，非Boss用“调侃语句池C”
            if is_boss then
                player.components.talker:Say("哇！金色传说！！")
            else
                local phrases_c = {
                    "别冲太快，注意队形。",
                    "能打就打，能苟就苟。",
                    "你负责挨打，我负责漂亮。",
                    "稳住，别送。",
                    "上吧，今日份主角交给你了。",
                }
                player.components.talker:Say(phrases_c[math.random(#phrases_c)])
            end
        else
            local bd = BOON_DEFINITIONS[boon_id]
            player.components.talker:Say(string.format("召唤了%s!", bd and bd.name or "随从"))
        end
    end
end

-- 跟踪并处理召唤物的死亡与复活（支持随机重抽）
function ModPlayerBoons:_TrackSummonedCreature(boon_id, creature, reroll_on_respawn)
    if not creature or not creature.components or not creature.components.health then return end

    if creature._boon_death_cb then
        creature:RemoveEventCallback("death", creature._boon_death_cb)
    end

    creature._boon_death_cb = function(inst)
        -- 从列表中移除
        if self.summoned_creatures[boon_id] then
            for i = #self.summoned_creatures[boon_id], 1, -1 do
                if self.summoned_creatures[boon_id][i] == inst then
                    table.remove(self.summoned_creatures[boon_id], i)
                    break
                end
            end
        end

        -- 延迟复活
        local is_boss_dead = inst:HasTag("epic") or inst:HasTag("largecreature")
        local delay = is_boss_dead and 180 or 60
        self.summon_respawn_tasks[boon_id] = self.summon_respawn_tasks[boon_id] or {}
        local task = self.inst:DoTaskInTime(delay, function()
            local player = self.inst
            if not player or not player:IsValid() then return end
            local def = BOON_DEFINITIONS[boon_id]
            local cfg = def and def.effect_value or {}
            local list = self.summoned_creatures[boon_id] or {}
            if #list >= (cfg.max_count or 1) then return end

            local x, _, z = player.Transform:GetWorldPosition()
            local ang = math.random() * 2 * math.pi
            local r = 2
            local sx = x + math.cos(ang) * r
            local sz = z + math.sin(ang) * r

            local prefab = reroll_on_respawn and self:PickRandomHostilePrefab() or cfg.prefab
            local newc = GLOBAL.SpawnPrefab(prefab)
            if not newc then return end
            newc.Transform:SetPosition(sx, 0, sz)
            newc:AddTag("companion"); newc:AddTag("summoned"); if newc:HasTag("monster") then newc:RemoveTag("monster") end
            if newc.components.combat then newc.components.combat:SetTarget(nil) end
            if newc.components.follower then newc.components.follower:SetLeader(player) end

            local is_boss2 = newc:HasTag("epic") or newc:HasTag("largecreature")
            if is_boss2 and newc.components.health then
                local maxh = newc.components.health.maxhealth or 0
                local newmax = math.min(math.floor(maxh * 0.5), 5000)
                if newmax > 0 then
                    newc.components.health:SetMaxHealth(newmax)
                    newc.components.health:SetPercent(1)
                end
            end
            if is_boss2 and newc.components.combat and newc.components.combat.defaultdamage then
                newc.components.combat:SetDefaultDamage(newc.components.combat.defaultdamage * 0.5)
            end

            if player.components.talker and (reroll_on_respawn or boon_id == "summon_boss") then
                if boon_id == "summon_boss" then
                    -- 第二被动：调侃语句池A
                    local phrases_b = {
                        "你先顶上，我在后面看着。",
                        "别慌，跟紧我就行。",
                        "小心点，别被打倒了。",
                        "看起来不太聪明的样子……正合适。",
                        "站我后面，别送命。",
                    }
                    player.components.talker:Say(phrases_b[math.random(#phrases_b)])
                elseif reroll_on_respawn then
                    -- 第三被动：Boss固定台词，非Boss用调侃语句池C
                    if is_boss2 then
                        player.components.talker:Say("哇！金色传说！！")
                    else
                        local phrases_c = {
                            "别冲太快，注意队形。",
                            "能打就打，能苟就苟。",
                            "你负责挨打，我负责漂亮。",
                            "稳住，别送。",
                            "上吧，今日份主角交给你了。",
                        }
                        player.components.talker:Say(phrases_c[math.random(#phrases_c)])
                    end
                end
            end

            table.insert(self.summoned_creatures[boon_id], newc)
            self:_TrackSummonedCreature(boon_id, newc, reroll_on_respawn)
        end)
        table.insert(self.summon_respawn_tasks[boon_id], task)
    end

    creature:ListenForEvent("death", creature._boon_death_cb)
end

-- 解散所有召唤物
function ModPlayerBoons:DismissAllSummons(boon_id)
    -- 移除现有召唤物
    if self.summoned_creatures[boon_id] then
        for _, creature in ipairs(self.summoned_creatures[boon_id]) do
            if creature and creature:IsValid() then
                creature:Remove()
            end
        end
        self.summoned_creatures[boon_id] = {}
    end
    -- 取消复活任务
    if self.summon_respawn_tasks[boon_id] then
        for _, t in ipairs(self.summon_respawn_tasks[boon_id]) do
            if t then pcall(function() t:Cancel() end) end
        end
        self.summon_respawn_tasks[boon_id] = {}
    end
    print("[商旅巡游录] 解散所有召唤物:", boon_id)
end

-- 应用被动恩惠效果
function ModPlayerBoons:ApplyBoonEffect(boon_id)
    local boon_def = BOON_DEFINITIONS[boon_id]
    if not boon_def then return end

    local player = self.inst
    local cleanup_fn = nil

    -- === 战士系效果 ===
    if boon_def.effect_type == "speed" then
        -- 移速加成
        if player.components.locomotor then
            player.components.locomotor:SetExternalSpeedMultiplier(player, "boon_" .. boon_id, boon_def.effect_value)
            cleanup_fn = function()
                if player and player:IsValid() and player.components.locomotor then
                    player.components.locomotor:RemoveExternalSpeedMultiplier(player, "boon_" .. boon_id)
                end
            end
        end

    elseif boon_def.effect_type == "damage_boost" then
        -- 攻击力提升
        if player.components.combat then
            -- 使用externaldamagemultipliers系统
            -- 正确的参数顺序：SetModifier(source, value, key)
            player.components.combat.externaldamagemultipliers:SetModifier(player, boon_def.effect_value, "boon_" .. boon_id)
            cleanup_fn = function()
                if player and player:IsValid() and player.components.combat and player.components.combat.externaldamagemultipliers then
                    player.components.combat.externaldamagemultipliers:RemoveModifier(player, "boon_" .. boon_id)
                end
            end
        end

    elseif boon_def.effect_type == "combo_system" then
        -- 连击系统（使用临时外部伤害倍率）
        local TEMP_KEY = "boon_combo_temp"

        local function OnDoAttack(inst, data)
            if not inst.components or not inst.components.combat then return end
            local target = (data and data.target) or inst.components.combat.target
            if not target or not target.GUID then return end
            local guid = target.GUID
            local stacks = self.combo_stacks[guid] or 0
            if stacks > 0 and inst.components.combat and inst.components.combat.externaldamagemultipliers then
                local mult = 1 + math.min(stacks, boon_def.effect_value) * 0.2
                inst.components.combat.externaldamagemultipliers:SetModifier(inst, mult, TEMP_KEY)
                -- 兜底：若未命中，极短时间后移除
                inst:DoTaskInTime(0, function()
                    if inst.components.combat and inst.components.combat.externaldamagemultipliers then
                        inst.components.combat.externaldamagemultipliers:RemoveModifier(inst, TEMP_KEY)
                    end
                end)
            end
        end

        local function OnAttackOther(inst, data)
            if data and data.target and data.target.GUID then
                local guid = data.target.GUID

                -- 命中后移除临时加成
                if inst.components.combat and inst.components.combat.externaldamagemultipliers then
                    inst.components.combat.externaldamagemultipliers:RemoveModifier(inst, TEMP_KEY)
                end

                -- 只保留当前目标的叠层
                for k, _ in pairs(self.combo_stacks) do
                    if k ~= guid then
                        self.combo_stacks[k] = nil
                        if self.combo_timers[k] then self.combo_timers[k]:Cancel() self.combo_timers[k] = nil end
                    end
                end

                -- 命中后为该目标叠层
                local newstacks = (self.combo_stacks[guid] or 0) + 1
                if newstacks > boon_def.effect_value then newstacks = boon_def.effect_value end
                self.combo_stacks[guid] = newstacks

                -- 3秒后自动清空该目标叠层
                if self.combo_timers[guid] then self.combo_timers[guid]:Cancel() end
                self.combo_timers[guid] = inst:DoTaskInTime(3, function()
                    self.combo_stacks[guid] = nil
                    self.combo_timers[guid] = nil
                end)

                if inst.components.talker and newstacks > 1 then
                    inst.components.talker:Say(string.format("连击 x%d!", newstacks))
                end
            end
        end

        player:ListenForEvent("doattack", OnDoAttack)
        player:ListenForEvent("onattackother", OnAttackOther)
        cleanup_fn = function()
            if player and player:IsValid() then
                player:RemoveEventCallback("doattack", OnDoAttack)
                player:RemoveEventCallback("onattackother", OnAttackOther)
                if player.components.combat and player.components.combat.externaldamagemultipliers then
                    player.components.combat.externaldamagemultipliers:RemoveModifier(player, TEMP_KEY)
                end
                for _, timer in pairs(self.combo_timers) do if timer then timer:Cancel() end end
                self.combo_stacks = {}
                self.combo_timers = {}
            end
        end

    -- === 法师系效果 ===
    elseif boon_def.effect_type == "poison_damage" then
        -- 毒伤效果
        local function OnAttackOther(inst, data)
            if data and data.target and data.target.components and data.target.components.health then
                self:ApplyPoisonDamage(data.target, boon_def.effect_value.damage, boon_def.effect_value.duration)
            end
        end

        player:ListenForEvent("onattackother", OnAttackOther)
        cleanup_fn = function()
            if player and player:IsValid() then
                player:RemoveEventCallback("onattackother", OnAttackOther)
            end
        end

    elseif boon_def.effect_type == "charge_attack" then
        -- 蓄力攻击：站定 1/3/5 秒后，分别提升 50%/200%/500%（阈值取最高命中）
        local TEMP_KEY = "boon_charge_temp"
        self.stationary_start_time = GLOBAL.GetTime()

        local function IsMoving(inst)
            if inst.components and inst.components.locomotor and inst.components.locomotor:WantsToMoveForward() then
                return true
            end
            if inst.sg and inst.sg:HasStateTag("moving") then
                return true
            end
            return false
        end

        local function OnLocomote(inst)
            if IsMoving(inst) then
                -- 重新开始计时
                self.stationary_start_time = GLOBAL.GetTime()
            end
        end

        local function OnAttackOther(inst)
            -- 计算站定时长（若刚刚移动过，会很小）
            local t = math.max(0, GLOBAL.GetTime() - (self.stationary_start_time or GLOBAL.GetTime()))
            local mult = 1.0
            if t >= 5 then
                mult = 6.0
            elseif t >= 3 then
                mult = 3.0
            elseif t >= 1 then
                mult = 1.5
            end
            if mult > 1.0 and inst.components.combat and inst.components.combat.externaldamagemultipliers then
                inst.components.combat.externaldamagemultipliers:SetModifier(inst, mult, TEMP_KEY)
                inst:DoTaskInTime(0, function()
                    if inst.components.combat and inst.components.combat.externaldamagemultipliers then
                        inst.components.combat.externaldamagemultipliers:RemoveModifier(inst, TEMP_KEY)
                    end
                end)
                if inst.components.talker then
                    inst.components.talker:Say(string.format("蓄力打击! x%.1f", mult))
                end
            end
        end

        player:ListenForEvent("locomote", OnLocomote)
        player:ListenForEvent("onattackother", OnAttackOther)
        cleanup_fn = function()
            if player and player:IsValid() then
                player:RemoveEventCallback("locomote", OnLocomote)
                player:RemoveEventCallback("onattackother", OnAttackOther)
                if player.components.combat and player.components.combat.externaldamagemultipliers then
                    player.components.combat.externaldamagemultipliers:RemoveModifier(player, TEMP_KEY)
                end
            end
        end

    elseif boon_def.effect_type == "teleport_skill" then
        -- 闪现技能 - 简化为按键触发
        -- 注意：这里我们简化实现，实际的双击检测需要更复杂的输入处理
        -- 暂时使用一个标记，让玩家可以通过聊天命令触发传送
        self.has_teleport = true

        cleanup_fn = function()
            self.has_teleport = false
        end

    -- === 召唤师系效果 ===
    elseif boon_def.effect_type == "summon_creature" then
        -- 召唤生物
        self:SummonCreature(boon_id, boon_def.effect_value)
        cleanup_fn = function()
            self:DismissAllSummons(boon_id)
        end

    -- === 农民系效果 ===
    elseif boon_def.effect_type == "plant_growth" then
        -- 植物生长加速
        local growth_task = player:DoPeriodicTask(boon_def.effect_value.check_interval, function()
            self:AcceleratePlantGrowth(boon_def.effect_value.range, boon_def.effect_value.growth_multiplier)
        end)
        cleanup_fn = function()
            if growth_task then
                growth_task:Cancel()
            end
        end

    elseif boon_def.effect_type == "auto_harvest" then
        -- 自动收获
        local harvest_task = player:DoPeriodicTask(boon_def.effect_value.check_interval, function()
            self:AutoHarvestCrops(boon_def.effect_value.range)
        end)
        cleanup_fn = function()
            if harvest_task then
                harvest_task:Cancel()
            end
        end

    elseif boon_def.effect_type == "harvest_blessing" then
        -- 丰收祝福
        local function OnPickSomething(inst, data)
            if data and data.object then
                -- 恢复三维属性
                if player.components.hunger then
                    player.components.hunger:DoDelta(boon_def.effect_value.hunger)
                end
                if player.components.sanity then
                    player.components.sanity:DoDelta(boon_def.effect_value.sanity)
                end
                if player.components.health then
                    player.components.health:DoDelta(boon_def.effect_value.health)
                end

                if player.components.talker then
                    player.components.talker:Say("丰收祝福!")
                end
            end
        end

        player:ListenForEvent("picksomething", OnPickSomething)
        cleanup_fn = function()
            if player and player:IsValid() then
                player:RemoveEventCallback("picksomething", OnPickSomething)
            end
        end
    end

    -- 保存清理函数
    if cleanup_fn then
        self.active_effects[boon_id] = cleanup_fn
    end

    print("[商旅巡游录] 应用被动恩惠效果:", boon_def.name)
end

-- 移除被动恩惠效果
function ModPlayerBoons:RemoveBoonEffect(boon_id)
    local cleanup_fn = self.active_effects[boon_id]
    if cleanup_fn then
        cleanup_fn()
        self.active_effects[boon_id] = nil

        local boon_def = BOON_DEFINITIONS[boon_id]
        print("[商旅巡游录] 移除被动恩惠效果:", boon_def and boon_def.name or boon_id)
    end
end

-- 重新应用所有装备的被动恩惠效果（用于加载存档后）
function ModPlayerBoons:ReapplyAllEffects()
    -- 先清理所有效果
    for boon_id, cleanup_fn in pairs(self.active_effects) do
        cleanup_fn()
    end
    self.active_effects = {}

    -- 重新应用装备的效果
    for _, boon_id in ipairs(self.equipped_boons) do
        self:ApplyBoonEffect(boon_id)
    end

    print("[商旅巡游录] 重新应用", #self.equipped_boons, "个被动恩惠效果")
end

function ModPlayerBoons:OnSave()
    return {
        favor = self.favor,
        unlocked_boons = self.unlocked_boons,
        equipped_boons = self.equipped_boons,
        max_equipped = self.max_equipped,
        -- 不保存运行时数据，重新加载时会重新初始化
    }
end

-- 客户端快照应用（用于RPC回传）
function ModPlayerBoons:ApplySnapshot(data)
    if not data then return end
    self.favor = data.favor or self.favor
    self.unlocked_boons = data.unlocked_boons or self.unlocked_boons
    self.equipped_boons = data.equipped_boons or self.equipped_boons
    self.max_equipped = data.max_equipped or self.max_equipped
    -- 重新应用效果（客户端显示用；实际效果由服务端生效即可）
    self:ReapplyAllEffects()
end

function ModPlayerBoons:OnLoad(data)
    if data then
        self.favor = data.favor or 0
        self.unlocked_boons = data.unlocked_boons or data.unlocked or {}  -- 兼容旧版本
        self.equipped_boons = data.equipped_boons or data.equipped or {}  -- 兼容旧版本
        self.max_equipped = data.max_equipped or 2

        -- 重新初始化运行时数据
        self.combo_stacks = {}
        self.combo_timers = {}
        self.charge_start_time = 0
        self.is_charging = false
        self.teleport_cooldown = 0
        self.last_move_time = 0
        self.move_key_count = 0
        self.summoned_creatures = {}

        -- 延迟重新应用效果，确保玩家完全加载
        self.inst:DoTaskInTime(1, function()
            self:ReapplyAllEffects()
        end)
    end
end

-- 植物生长加速
function ModPlayerBoons:AcceleratePlantGrowth(range, multiplier)
    local player = self.inst
    if not player or not player:IsValid() then return end

    local x, _, z = player.Transform:GetWorldPosition()
    local ents = GLOBAL.TheSim:FindEntities(x, 0, z, range, {"plant"})

    for _, ent in ipairs(ents) do
        if ent and ent:IsValid() and ent.components.growable then
            -- 检查是否正在生长
            if ent.components.growable:IsGrowing() and ent.components.growable.targettime then
                local current_time = GLOBAL.GetTime()
                local remaining = ent.components.growable.targettime - current_time

                if remaining > 0 then
                    -- 计算新的目标时间（加速生长）
                    local new_remaining = remaining / multiplier
                    local new_target_time = current_time + new_remaining

                    -- 取消当前任务并创建新的加速任务
                    if ent.components.growable.task then
                        ent.components.growable.task:Cancel()
                    end

                    ent.components.growable.targettime = new_target_time
                    ent.components.growable.task = ent:DoTaskInTime(new_remaining, function()
                        if ent and ent:IsValid() and ent.components.growable then
                            ent.components.growable:DoGrowth()
                        end
                    end)
                end
            end
        end
    end
end

-- 自动收获作物
function ModPlayerBoons:AutoHarvestCrops(range)
    local player = self.inst
    if not player or not player:IsValid() then return end

    local x, _, z = player.Transform:GetWorldPosition()
    local ents = GLOBAL.TheSim:FindEntities(x, 0, z, range, {"pickable"})

    for _, ent in ipairs(ents) do
        if ent and ent:IsValid() and ent.components.pickable then
            -- 检查是否可以采集
            if ent.components.pickable:CanBePicked() then
                -- 自动采集
                local loot = ent.components.pickable:Pick(player)
                if loot then
                    -- 尝试将物品添加到玩家背包
                    if player.components.inventory then
                        local success = player.components.inventory:GiveItem(loot)
                        if not success then
                            -- 如果背包满了，掉落在地上
                            loot.Transform:SetPosition(x, 0, z)
                        end
                    else
                        -- 如果没有背包组件，掉落在地上
                        loot.Transform:SetPosition(x, 0, z)
                    end

                    -- 触发采集事件，让丰收祝福生效
                    player:PushEvent("picksomething", {object = ent, loot = loot})
                end
            end
        end
    end
end

return ModPlayerBoons
