-- 测试合约系统网络架构
print("=== 测试合约系统网络架构 ===")

-- 模拟网络环境
local function CreateMockNetworkEnvironment()
    print("\n--- 创建模拟网络环境 ---")
    
    -- 模拟全局环境
    GLOBAL = GLOBAL or {}
    GLOBAL.TheNet = {
        GetIsServer = function() return true end,
        Announce = function(msg) print("服务器公告:", msg) end
    }
    
    GLOBAL.TheShard = {
        GetShardId = function() return "forest" end,
        IsMaster = function() return true end,
        ListenForEvent = function(self, event, fn)
            print("分片监听事件:", event)
        end,
        SendShardRPC = function(self, target, command, data)
            print("发送分片RPC:", target, command, data and "有数据" or "无数据")
        end
    }
    
    GLOBAL.TheWorld = {
        GUID = "world_123",
        net = true,
        components = {
            modworld_global = {
                contracts = {
                    {
                        id = "test_contract_1",
                        type = "kill",
                        target_data = {prefab = "spider", name = "蜘蛛", goal = 10},
                        progress = 3,
                        goal = 10,
                        completed = false
                    }
                },
                contract_version = 1
            }
        }
    }
    
    -- 模拟网络变量
    GLOBAL.net_string = function(guid, name, event)
        return {
            set = function(self, value) 
                print("设置网络变量", name, ":", string.sub(value or "", 1, 50) .. "...")
            end,
            value = function(self) return '{"contracts":[{"id":"test"}],"timestamp":123}' end
        }
    end
    
    GLOBAL.net_int = function(guid, name, event)
        return {
            set = function(self, value) 
                print("设置网络整数", name, ":", value)
            end,
            value = function(self) return 1 end
        }
    end
    
    -- 模拟RPC系统
    GLOBAL.AddModRPCHandler = function(mod, command, handler)
        print("注册RPC处理器:", mod, command)
    end
    
    GLOBAL.SendModRPCToServer = function(mod, command, ...)
        print("发送RPC到服务器:", mod, command, ...)
    end
    
    GLOBAL.SendModRPCToClient = function(client, mod, command, ...)
        print("发送RPC到客户端:", mod, command, ...)
    end
    
    GLOBAL.GetClientTable = function(userid)
        return {userid = userid}
    end
    
    -- 模拟JSON
    GLOBAL.json = {
        encode = function(data)
            return string.format('{"type":"%s","count":%d}', 
                data.contracts and data.contracts[1] and data.contracts[1].type or "unknown",
                data.contracts and #data.contracts or 0)
        end,
        decode = function(str)
            return {contracts = {{id = "decoded_test", type = "kill"}}, timestamp = 456}
        end
    }
    
    GLOBAL.GetTime = function() return 1234567890 end
    
    print("✓ 模拟网络环境创建完成")
end

-- 测试网络系统初始化
local function TestNetworkInit()
    print("\n--- 测试网络系统初始化 ---")
    
    local ContractNetwork = require("scripts/systems/contract_network")
    
    -- 测试初始化
    ContractNetwork:Init()
    print("✓ 网络系统初始化完成")
    
    -- 测试权威服务器判断
    local is_authority = ContractNetwork:IsAuthorityServer()
    print("✓ 权威服务器判断:", is_authority and "是" or "否")
    
    return ContractNetwork
end

-- 测试RPC处理
local function TestRPCHandling(network)
    print("\n--- 测试RPC处理 ---")
    
    -- 模拟玩家
    local mock_player = {
        userid = "player_123",
        components = {
            inventory = {
                maxslots = 15,
                itemslots = {
                    [1] = {
                        prefab = "goldnugget",
                        components = {
                            stackable = {
                                StackSize = function() return 10 end
                            }
                        }
                    }
                }
            },
            talker = {
                Say = function(self, msg) print("玩家说:", msg) end
            }
        }
    }
    
    -- 测试交付合约RPC
    print("测试交付合约RPC...")
    network:HandleDeliverContract(mock_player, 1)
    
    -- 测试请求合约RPC
    print("测试请求合约RPC...")
    network:HandleRequestContracts(mock_player)
    
    -- 测试进度同步RPC
    print("测试进度同步RPC...")
    network:HandleSyncProgress(mock_player, "test_contract_1", 2, {type = "kill", target = "spider"})
    
    print("✓ RPC处理测试完成")
end

-- 测试分片通信
local function TestShardCommunication(network)
    print("\n--- 测试分片通信 ---")
    
    -- 测试进度更新分片通信
    local progress_data = {
        command = "contract_progress_update",
        contract_id = "test_contract_1",
        progress_delta = 1,
        event_data = {type = "kill", target = "spider"},
        player_id = "player_123"
    }
    
    print("测试分片进度更新...")
    network:HandleShardProgressUpdate("caves", progress_data)
    
    -- 测试合约同步分片通信
    local sync_data = {
        contracts = {
            {id = "sync_test", type = "deliver", progress = 5, goal = 10}
        },
        version = 2
    }
    
    print("测试分片合约同步...")
    network:HandleShardContractSync("forest", sync_data)
    
    print("✓ 分片通信测试完成")
end

-- 测试网络变量同步
local function TestNetVarSync(network)
    print("\n--- 测试网络变量同步 ---")
    
    -- 测试同步到客户端
    print("测试同步合约到客户端...")
    network:SyncContractsToClients()
    
    -- 测试同步到所有分片和客户端
    print("测试同步到所有分片...")
    network:SyncContractsToAll()
    
    -- 测试客户端数据变化处理
    print("测试客户端数据变化处理...")
    network:OnContractDataChanged()
    
    print("✓ 网络变量同步测试完成")
end

-- 测试客户端功能
local function TestClientFunctions(network)
    print("\n--- 测试客户端功能 ---")
    
    -- 模拟客户端环境
    GLOBAL.TheNet.GetIsServer = function() return false end
    
    -- 测试客户端交付请求
    print("测试客户端交付请求...")
    network:ClientDeliverContract(1)
    
    -- 测试客户端请求合约数据
    print("测试客户端请求合约数据...")
    network:ClientRequestContracts()
    
    -- 测试进度上报
    print("测试进度上报...")
    network:ReportProgress("test_contract_1", 1, {type = "kill"})
    
    -- 恢复服务端环境
    GLOBAL.TheNet.GetIsServer = function() return true end
    
    print("✓ 客户端功能测试完成")
end

-- 测试错误处理
local function TestErrorHandling(network)
    print("\n--- 测试错误处理 ---")
    
    -- 测试无效参数
    print("测试无效参数处理...")
    network:HandleDeliverContract(nil, nil)
    network:HandleSyncProgress(nil, nil, nil, nil)
    network:HandleShardProgressUpdate(nil, nil)
    
    -- 测试无效JSON
    print("测试无效JSON处理...")
    local old_decode = GLOBAL.json.decode
    GLOBAL.json.decode = function() error("JSON解析错误") end
    network:OnContractDataChanged()
    GLOBAL.json.decode = old_decode
    
    print("✓ 错误处理测试完成")
end

-- 运行所有测试
local function RunAllTests()
    CreateMockNetworkEnvironment()
    local network = TestNetworkInit()
    TestRPCHandling(network)
    TestShardCommunication(network)
    TestNetVarSync(network)
    TestClientFunctions(network)
    TestErrorHandling(network)
end

-- 执行测试
RunAllTests()

print("\n=== 网络系统测试完成 ===")
print("阶段1完成状态:")
print("1. ✓ RPC系统：支持交付、请求、进度同步")
print("2. ✓ 分片通信：支持跨分片进度更新和合约同步")
print("3. ✓ 网络变量：支持合约数据和进度的客户端同步")
print("4. ✓ 权威服务器：Forest分片作为权威源")
print("5. ✓ 客户端支持：UI通过RPC与服务端交互")
print("6. ✓ 错误处理：网络异常和无效数据的安全处理")
print("7. ✓ 降级兼容：网络不可用时降级到直接调用")
print("\n下一步：实现阶段2 - 跨模组动态目标池")
