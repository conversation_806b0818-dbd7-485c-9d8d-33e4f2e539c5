---@meta

---@class component_weapon
---@field inst idk
---@field damage idk
---@field attackrange idk
---@field hitrange idk
---@field onattack fun(inst:ent,attacker:ent|nil,target:ent|nil,...:any):...
---@field onprojectilelaunch idk
---@field onprojectilelaunched idk
---@field projectile idk
---@field stimuli idk
---@field overridestimulifn idk
---@field electric_damage_mult idk
---@field electric_wet_damage_mult idk
---@field attackwearmultipliers idk
---@field projectile_offset idk