-- 确保能访问全局变量
local GLOBAL = rawget(_G, "GLOBAL") or _G

local Screen = require "widgets/screen"
local Widget = require "widgets/widget"
local Text = require "widgets/text"
local Image = require "widgets/image"
local ImageButton = require "widgets/imagebutton"
local TEMPLATES = require "widgets/redux/templates"

-- 安全设置颜色的小工具：支持 {r,g,b[,a]} 表，自动补 a=1
local function SetColourSafe(widget, col)
    if widget and widget.SetColour and type(col) == "table" then
        widget:SetColour(col[1] or 1, col[2] or 1, col[3] or 1, col[4] or 1)
    end
end

local CaravanScreen = Class(Screen, function(self, owner)
    Screen._ctor(self, "CaravanScreen")
    self.owner = owner

    -- 半透明背景
    self.bg = self:AddChild(ImageButton("images/global.xml", "square.tex"))
    self.bg.image:SetVRegPoint(ANCHOR_MIDDLE)
    self.bg.image:SetHRegPoint(ANCHOR_MIDDLE)
    self.bg.image:SetVAnchor(ANCHOR_MIDDLE)
    self.bg.image:SetHAnchor(ANCHOR_MIDDLE)
    self.bg.image:SetScaleMode(SCALEMODE_FILLSCREEN)
    self.bg.image:SetTint(0, 0, 0, 0.5)
    self.bg:SetOnClick(function() self:Close() end)
    if self.bg.SetHelpTextMessage then
        self.bg:SetHelpTextMessage("")
    end

    -- 主面板容器 - 正确设置居中
    self.root = self:AddChild(Widget("root"))
    self.root:SetHAnchor(ANCHOR_MIDDLE)
    self.root:SetVAnchor(ANCHOR_MIDDLE)
    self.root:SetPosition(0, 0)

    -- 主面板（增大尺寸，给内容更多空间）
    self.panel = self.root:AddChild(TEMPLATES.RectangleWindow(800, 700, "商旅巡游录", nil))

    -- 内容区域（更稳健）
    local body = self.panel.body or self.panel
    self.content = body:AddChild(Widget("content"))
    self.content:SetPosition(0, 0) -- 居中，整体在窗口内部布局

    -- 标签页按钮
    self.tabs = {}
    self.current_tab = "mutators"

    local tab_names = {
        {id = "mutators", text = "今日词条"},
        {id = "contracts", text = "合约任务"},
        {id = "boons", text = "被动恩惠"},
        {id = "sacrifice", text = "献祭轮盘"},
        {id = "caravan", text = "商队信息"}
    }

    -- 标签页移至底部，增大按钮尺寸
    for i, tab in ipairs(tab_names) do
        local btn = self.content:AddChild(TEMPLATES.StandardButton(
            function() self:SwitchTab(tab.id) end,
            tab.text,
            {140, 50}
        ))
        btn:SetPosition(-280 + (i-1) * 140, -280) -- 底部靠下，调整间距
        self.tabs[tab.id] = btn
    end

    -- 内容显示区域
    self.info_area = self.content:AddChild(Widget("info_area"))
    self.info_area:SetPosition(0, 0)

    -- 文本显示（使用 ScrollingTextBox 支持长文本滚动）
    local ok, ScrollingText = pcall(require, "widgets/scrollingtextbox")
    if not ok then
        -- 理论上不会失败；为了稳妥加个兜底
        ScrollingText = nil
    end

    if ScrollingText then
        -- 直接用官方滚动文本控件，增大尺寸
        self.info_text = self.info_area:AddChild(ScrollingText("", 700, 500, CHATFONT, 28))
        -- 让滚动区域居中稍向上，避免顶到边框
        self.info_text:SetPosition(0, 30)

        if self.info_text.text then
            self.info_text.text:SetHAlign(ANCHOR_LEFT)
            self.info_text.text:SetVAlign(ANCHOR_TOP)
            -- 关键：不要再改内部 text 的 RegionSize，交给 ScrollingText 自己管理
        end
    else
        -- 极端兜底：没有 ScrollingTextBox 就退化为普通 Text（没有滚动）
        self.info_text = self.info_area:AddChild(Text(CHATFONT, 28, ""))
        SetColourSafe(self.info_text, {1,1,1,1})
        self.info_text:SetRegionSize(700, 500)
        self.info_text:SetHAlign(ANCHOR_LEFT)
        self.info_text:SetVAlign(ANCHOR_TOP)
        self.info_text:SetPosition(-350, 250)
    end

    -- 操作按钮区域（紧贴底部标签之上）
    self.action_area = self.info_area:AddChild(Widget("action_area"))
    self.action_area:SetPosition(0, -200)

    -- 创建操作按钮（初始隐藏）
    self.action_buttons = {}

    self:RefreshData()
    self:SwitchTab("mutators")

    -- 设置默认焦点到第一个标签按钮，改善手柄/键盘操作体验
    self.default_focus = self.tabs["mutators"] or self.root

    -- 初始化网络数据缓存
    self.cached_contracts = {}
    self.last_contract_version = 0

    -- 初始化本地缓存
    self.cached_contracts = nil
    self.cached_mutators = nil
    self.cached_caravan = nil

    -- 提供网络回调入口
    function self:OnMutatorsDataUpdated(mutators)
        self.cached_mutators = mutators
        if self.current_tab == "mutators" then
            self:CreateMutatorsUI()
        end
    end

    function self:OnCaravanDataUpdated(caravan)
        self.cached_caravan = caravan
        if self.current_tab == "caravan" then
            self:CreateCaravanUI()
        end
    end

    -- 请求最新数据（合约、词条、商队）
    SendModRPCToServer(GetModRPC("thinking","contract_request"))
    SendModRPCToServer(GetModRPC("thinking","mutators_request"))
    SendModRPCToServer(GetModRPC("thinking","caravan_request"))
end)

function CaravanScreen:SwitchTab(tab_id)
    self.current_tab = tab_id

    -- 更新按钮状态
    for id, btn in pairs(self.tabs) do
        if id == tab_id then
            btn:SetTextColour(1, 1, 0, 1) -- 黄色表示选中
        else
            btn:SetTextColour(1, 1, 1, 1) -- 白色表示未选中
        end
    end

    -- 隐藏所有专用UI
    local ui_elements = {
        self.boons_ui,
        self.mutators_ui,
        self.contracts_ui,
        self.reputation_ui,
        self.sacrifice_ui,
        self.caravan_ui
    }

    for _, ui in pairs(ui_elements) do
        if ui then
            ui:Hide()
        end
    end

    -- 隐藏默认文本显示（防止与专用UI重叠）
    if self.info_text then
        self.info_text:Hide()
    end

    -- 显示当前标签页的UI
    if tab_id == "boons" and self.boons_ui then
        self.boons_ui:Show()
    elseif tab_id == "mutators" and self.mutators_ui then
        self.mutators_ui:Show()
    elseif tab_id == "contracts" and self.contracts_ui then
        self.contracts_ui:Show()
    elseif tab_id == "sacrifice" and self.sacrifice_ui then
        self.sacrifice_ui:Show()
        -- 启动每秒刷新任务（仅在献祭页）
        if (not self._sac_timer_task) and self.owner and self.owner.DoPeriodicTask then
            self._sac_timer_task = self.owner:DoPeriodicTask(1, function()
                -- 若切页或关闭界面则清理
                local screen = TheFrontEnd and TheFrontEnd:GetActiveScreen() or nil
                if not screen or screen ~= self or self.current_tab ~= "sacrifice" then
                    if self._sac_timer_task then self._sac_timer_task:Cancel(); self._sac_timer_task = nil end
                    return
                end
                self:UpdateSacrificeUIContent()
            end)
        end
    elseif tab_id == "caravan" and self.caravan_ui then
        self.caravan_ui:Show()
    end

    -- 清除旧的操作按钮
    for _, btn in pairs(self.action_buttons) do
        btn:Kill()
    end
    self.action_buttons = {}

    -- 根据标签页添加相应的操作按钮
    self:CreateActionButtons(tab_id)

    self:UpdateContent()

    -- 切换标签后重置滚动到顶部
    if self.info_text then
        if self.info_text.ScrollToTop then
            self.info_text:ScrollToTop()
        elseif self.info_text.ResetView then
            self.info_text:ResetView()
        end
    end
end

function CaravanScreen:CreateActionButtons(tab_id)
    if tab_id == "mutators" then
        -- 重掷词条按钮
        local player_reroll = self.owner and self.owner.components and self.owner.components.modplayer_reroll
        local can_reroll = player_reroll and player_reroll:CanReroll()

        local reroll_btn = self.action_area:AddChild(TEMPLATES.StandardButton(
            function() self:DoReroll() end,
            can_reroll and "重掷词条" or "已重掷",
            {120, 40}
        ))
        reroll_btn:SetPosition(0, 0) -- 居中显示

        -- 如果不能重掷，禁用按钮
        if not can_reroll then
            reroll_btn:SetTextColour(0.5, 0.5, 0.5, 1) -- 灰色
        end

        table.insert(self.action_buttons, reroll_btn)

    elseif tab_id == "boons" then
        -- 洗点按钮
        local respec_btn = self.action_area:AddChild(TEMPLATES.StandardButton(
            function() self:DoRespec() end,
            "洗点重置",
            {120, 40}
        ))
        respec_btn:SetPosition(0, 0)
        -- 打开被动恩惠页时，请求一次快照（确保多人环境从服务器拉最新）
        SendModRPCToServer(GetModRPC("thinking","boons_request"))

        table.insert(self.action_buttons, respec_btn)
    end
    -- 其他标签页暂时不需要操作按钮
end

function CaravanScreen:DoReroll()
    -- 播放标准HUD点击音效
    local s = TheFrontEnd and TheFrontEnd:GetSound()
    if s and s.PlaySound then
        s:PlaySound("dontstarve/HUD/click_move")
    end

    -- 通过RPC请求服务端执行重掷
    SendModRPCToServer(GetModRPC("thinking","mutators_reroll"))
end

function CaravanScreen:DoRespec()
    -- 洗点功能
    local player_boons = self.owner and self.owner.components and self.owner.components.modplayer_boons
    if not player_boons then
        if self.owner and self.owner.components and self.owner.components.talker then
            self.owner.components.talker:Say("无法获取被动技能信息")
        end
        return
    end

    -- 计算洗点费用（已装备被动恩惠数量 * 10）
    local equipped_tbl = player_boons.equipped_boons or {}
    local equipped_count = #equipped_tbl
    local respec_cost = equipped_count * 10
    local favor = player_boons.favor or 0

    -- 如果没有装备任何被动恩惠，不需要洗点
    if equipped_count == 0 then
        if self.owner.components.talker then
            self.owner.components.talker:Say("没有装备任何被动技能，无需洗点")
        end
        return
    end

    if favor >= respec_cost then
        -- 通过RPC让服务端执行洗点，UI刷新由回传快照触发
        SendModRPCToServer(GetModRPC("thinking","boons_action"), "respec")
        local s = TheFrontEnd and TheFrontEnd:GetSound()
        if s and s.PlaySound then s:PlaySound("dontstarve/HUD/click_move") end
    else
        if self.owner.components.talker then
            self.owner.components.talker:Say(string.format("✗ 洗点需要 %d 星尘，当前只有 %d", respec_cost, favor))
        end
    end
end

function CaravanScreen:UpdateContent()
    local content = ""

    if self.current_tab == "mutators" then
        -- 使用专用UI
        self:CreateMutatorsUI()
        return

    elseif self.current_tab == "contracts" then
        -- 使用专用UI
        self:CreateContractsUI()
        return

    elseif self.current_tab == "boons" then
        -- 被动恩惠页面现在使用新的UI布局
        self:UpdateBoonsContent()
        return

    elseif self.current_tab == "sacrifice" then
        self:CreateSacrificeUI()
        return

    elseif self.current_tab == "caravan" then
        -- 使用专用UI
        self:CreateCaravanUI()
        return
    end

    self.info_text:SetString(content)

    -- 兼容不同滚动控件的安全刷新/复位
    if self.info_text then
        if self.info_text.ScrollToTop then
            self.info_text:ScrollToTop()
        elseif self.info_text.ResetView then
            self.info_text:ResetView()
        end
    end
end

-- 新的被动恩惠UI更新方法（优化版）
function CaravanScreen:UpdateBoonsContent()
    local player_boons = self.owner and self.owner.components and self.owner.components.modplayer_boons
    if not player_boons then
        -- 如果没有数据，显示提示信息
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString("无法获取被动技能信息")
        end
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.boons_ui then
        self:CreateBoonsUI(player_boons)
    else
        -- 如果UI已存在，只更新内容
        self:UpdateBoonsUIContent(player_boons)
    end
end

-- 创建今日词条专用UI
function CaravanScreen:CreateMutatorsUI()
    local mutators_data = self.cached_mutators
    if not mutators_data or #mutators_data == 0 then
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString(self:IsSyncing() and "词条数据同步中..." or "暂无词条信息")
        end
        return
    end

    if not self.mutators_ui then
        self:CreateMutatorsUIStructure()
    end

    self:UpdateMutatorsUIContent(mutators_data)
end

-- 创建今日词条UI结构
function CaravanScreen:CreateMutatorsUIStructure()
    -- 创建词条UI容器
    self.mutators_ui = self.info_area:AddChild(Widget("mutators_ui"))
    self.mutators_ui:SetPosition(0, 30)

    -- 创建标题
    self.mutators_title = self.mutators_ui:AddChild(Text(CHATFONT, 32, "今日词条"))
    SetColourSafe(self.mutators_title, {1, 1, 0, 1})
    self.mutators_title:SetPosition(0, 150)

    -- 创建词条容器
    self.mutators_container = self.mutators_ui:AddChild(Widget("mutators_container"))
    self.mutators_container:SetPosition(0, 80)

    -- 创建图例
    self:CreateMutatorsLegend()
end

-- 创建词条图例
function CaravanScreen:CreateMutatorsLegend()
    local legend_y = -140

    -- 图例标题
    local legend_title = self.mutators_ui:AddChild(Text(CHATFONT, 26, "词条类型:"))
    SetColourSafe(legend_title, {1, 1, 1, 1})
    legend_title:SetPosition(0, legend_y)

    -- 图例项目
    local legends = {
        {text = "✓ 有利词条", color = {0.2, 0.8, 0.2, 1}, pos = {-160, legend_y - 30}},
        {text = "✗ 挑战词条", color = {0.8, 0.2, 0.2, 1}, pos = {-50, legend_y - 30}},
        {text = "◈ 平衡词条", color = {0.6, 0.6, 0.6, 1}, pos = {60, legend_y - 30}},
        {text = "★ 事件词条", color = {1, 0.8, 0.2, 1}, pos = {170, legend_y - 30}}
    }

    for _, legend in ipairs(legends) do
        local legend_text = self.mutators_ui:AddChild(Text(CHATFONT, 22, legend.text))
        SetColourSafe(legend_text, legend.color)
        legend_text:SetPosition(legend.pos[1], legend.pos[2])
    end
end

-- 更新词条UI内容
function CaravanScreen:UpdateMutatorsUIContent(world_comp)
    -- 清除旧的词条
    if self.mutator_cards then
        for _, card in pairs(self.mutator_cards) do
            card:Kill()
        end
    end
    self.mutator_cards = {}

    -- 创建词条卡片
    for i, mutator in ipairs(world_comp.mutators) do
        self:CreateMutatorCard(i, mutator)
    end
end

-- 创建词条卡片
function CaravanScreen:CreateMutatorCard(index, mutator)
    local card_y = 40 - (index - 1) * 60  -- 向下移动40像素，避免盖住标题

    -- 创建卡片容器
    local card = self.mutators_container:AddChild(Widget("mutator_card_" .. index))
    card:SetPosition(0, card_y)

    -- 确定词条类型颜色
    local type_color = {0.5, 0.5, 0.5, 1} -- 默认灰色
    local type_icon = "◈"

    if mutator.type == "positive" then
        type_color = {0.2, 0.8, 0.2, 1} -- 绿色
        type_icon = "✓"
    elseif mutator.type == "negative" then
        type_color = {0.8, 0.2, 0.2, 1} -- 红色
        type_icon = "✗"
    elseif mutator.type == "event" then
        type_color = {1, 0.8, 0.2, 1} -- 黄色
        type_icon = "★"
    end

    -- 创建卡片背景（使用更透明的背景）
    local card_bg = card:AddChild(Image("images/global.xml", "square.tex"))
    card_bg:SetScale(1.0, 0.3)
    card_bg:SetTint(0.2, 0.2, 0.3, 0.3)  -- 更浅的背景色，更透明
    card_bg:SetPosition(0, 0)

    -- 创建词条文本
    local mutator_text = card:AddChild(Text(CHATFONT, 24, string.format("[%d] %s %s", index, type_icon, mutator.desc or "未知词条")))
    SetColourSafe(mutator_text, type_color)
    mutator_text:SetPosition(0, 0)

    -- 存储卡片引用
    self.mutator_cards[index] = card
end



-- 创建合约任务专用UI
function CaravanScreen:CreateContractsUI()
    local contract_data = self:GetContractData()
    if not contract_data or not contract_data.contracts then
        -- 如果没有数据，显示同步状态
        if self.info_text then
            self.info_text:Show()
            if self:IsSyncing() then
                self.info_text:SetString("合约数据同步中...")
            else
                self.info_text:SetString("暂无合约数据")
            end
        end
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.contracts_ui then
        self:CreateContractsUIStructure()
    end

    -- 更新内容
    self:UpdateContractsUIContent(contract_data)
end

-- 创建合约任务UI结构
function CaravanScreen:CreateContractsUIStructure()
    -- 创建合约UI容器
    self.contracts_ui = self.info_area:AddChild(Widget("contracts_ui"))
    self.contracts_ui:SetPosition(0, 30)

    -- 1) 标题与统计稍上移，留出上方gutter
    self.contracts_title = self.contracts_ui:AddChild(Text(CHATFONT, 32, "合约任务"))
    SetColourSafe(self.contracts_title, {1, 1, 0, 1})
    self.contracts_title:SetPosition(0, 200)

    -- 创建状态统计
    self.contracts_stats = self.contracts_ui:AddChild(Text(CHATFONT, 22, ""))
    SetColourSafe(self.contracts_stats, {0.8, 0.8, 0.8, 1})
    self.contracts_stats:SetPosition(0, 160)

    -- 2) 先建"图例层"，再建"列表层"
    self.contracts_legend = self.contracts_ui:AddChild(Widget("contracts_legend"))
    self.contracts_legend:SetPosition(0, -210)  -- 更靠下，给列表留空间

    -- 创建图例
    self:CreateContractsLegend()

    -- 3) 列表容器最后建，位于图例之上
    self.contracts_container = self.contracts_ui:AddChild(Widget("contracts_container"))
    self.contracts_container:SetPosition(0, 100) -- 提高起点

    -- 可选：把图例整体移到背后一层（以防后续又有子控件插入）
    if self.contracts_legend.MoveToBack then
        self.contracts_legend:MoveToBack()
    end
end

-- 创建合约图例
function CaravanScreen:CreateContractsLegend()
    local legend_y = 0

    -- 图例标题
    local legend_title = self.contracts_legend:AddChild(Text(CHATFONT, 26, "任务状态:"))
    SetColourSafe(legend_title, {1, 1, 1, 1})
    legend_title:SetPosition(0, legend_y)

    -- 图例项目
    local legends = {
        {text = "✓ 已完成", color = {0.2, 0.8, 0.2, 1}, pos = {-100, legend_y - 30}},
        {text = "○ 进行中", color = {0.8, 0.8, 0.2, 1}, pos = {100, legend_y - 30}}
    }

    for _, legend in ipairs(legends) do
        local legend_text = self.contracts_legend:AddChild(Text(CHATFONT, 22, legend.text))
        SetColourSafe(legend_text, legend.color)
        legend_text:SetPosition(legend.pos[1], legend.pos[2])
    end
end

-- 更新合约UI内容
function CaravanScreen:UpdateContractsUIContent(contract_data)
    if not contract_data or not contract_data.contracts then return end

    -- 更新统计信息
    local completed_count = 0
    local total_count = #contract_data.contracts
    for _, contract in ipairs(contract_data.contracts) do
        if contract.completed then
            completed_count = completed_count + 1
        end
    end

    if self.contracts_stats then
        local stats_text = string.format("进度: %d/%d 已完成", completed_count, total_count)
        self.contracts_stats:SetString(stats_text)
    end

    -- 清除旧的合约
    if self.contract_cards then
        for _, card in pairs(self.contract_cards) do
            card:Kill()
        end
    end
    self.contract_cards = {}

    if total_count == 0 then
        -- 显示无任务信息
        local no_contracts = self.contracts_container:AddChild(Text(CHATFONT, 28, "暂无合约任务"))
        SetColourSafe(no_contracts, {0.6, 0.6, 0.6, 1})
        no_contracts:SetPosition(0, 0)
        return
    end

    -- 创建合约卡片
    for i, contract in ipairs(contract_data.contracts) do
        self:CreateContractCard(i, contract)
    end
end

-- 创建合约卡片
function CaravanScreen:CreateContractCard(index, contract)
    local ROW_H = 90
    local card_y = 20 - (index - 1) * ROW_H  -- 原来是 40/85，间距略小

    -- 创建卡片容器
    local card = self.contracts_container:AddChild(Widget("contract_card_" .. index))
    card:SetPosition(0, card_y)

    -- 确定状态颜色
    local status_color = {0.8, 0.8, 0.2, 1} -- 默认黄色（进行中）
    local status_icon = "○"

    if contract.completed then
        status_color = {0.2, 0.8, 0.2, 1} -- 绿色（已完成）
        status_icon = "✓"
    end

    -- 创建卡片背景（根据完成状态调整颜色）
    local card_bg = card:AddChild(Image("images/global.xml", "square.tex"))
    card_bg:SetScale(1.15, 0.40)  -- 背景稍微瘦一点，更利于排布
    if contract.completed then
        card_bg:SetTint(0.1, 0.3, 0.1, 0.4)  -- 完成的任务用绿色背景
    else
        card_bg:SetTint(0.2, 0.2, 0.3, 0.3)  -- 进行中的任务用蓝色背景
    end
    card_bg:SetPosition(0, 0)

    -- 获取任务类型名称
    local type_name = ""
    if contract.type == "kill" then
        type_name = "击杀"
    elseif contract.type == "deliver" then
        type_name = "交付"
    elseif contract.type == "build" then
        type_name = "建设"
    elseif contract.type == "collect" then
        type_name = "采集"
    elseif contract.type == "craft" then
        type_name = "制作"
    else
        type_name = "未知"
    end

    -- 创建任务标题
    local target_name = (contract.target_data and contract.target_data.name) or "未知"
    local title_text = string.format("[%d] %s %s：%s", index, status_icon, type_name, target_name)
    local title = card:AddChild(Text(CHATFONT, 22, title_text))
    SetColourSafe(title, status_color)
    title:SetPosition(0, 10)

    -- 创建进度文本（左对齐）
    local progress_text = string.format("进度: %d/%d", contract.progress or 0, contract.goal or 0)
    local progress = card:AddChild(Text(CHATFONT, 18, progress_text))
    SetColourSafe(progress, {0.8, 0.8, 0.8, 1})
    progress:SetHAlign(ANCHOR_LEFT)
    progress:SetPosition(-300, -10)  -- 以卡片中心为0，左移到左边缘内侧

    -- 创建奖励文本（右对齐）
    local reward_text = string.format("奖励: %d星尘", contract.reward_favor or 0)
    if contract.reward_rep then
        for faction, rep_amount in pairs(contract.reward_rep) do
            reward_text = reward_text .. string.format("，%s声望+%d", faction, rep_amount)
        end
    end
    local reward = card:AddChild(Text(CHATFONT, 18, reward_text))
    SetColourSafe(reward, {0.8, 0.8, 0.8, 1})
    reward:SetHAlign(ANCHOR_RIGHT)
    reward:SetPosition(300, -10)     -- 右移到右边缘内侧

    -- 为未完成的合约添加操作按钮
    if not contract.completed then
        self:CreateContractActionButton(card, index, contract)
    end

    -- 存储卡片引用
    self.contract_cards[index] = card
end

-- 创建合约操作按钮
function CaravanScreen:CreateContractActionButton(card, index, contract)
    local button_text = ""
    local button_enabled = false
    local button_action = nil

    if contract.type == "deliver" then
        button_enabled = self:CanPlayerDeliverContract(contract)
        if button_enabled then
            button_text = "交付物品"
        else
            local result = self._delivery_check_result
            if result and result.has_total > 0 then
                button_text = "物品不符合要求"
            else
                button_text = "缺少物品"
            end
        end
        button_action = function() self:DoDeliverContract(index, contract) end

    elseif contract.type == "kill" then
        button_text = "查看目标"
        button_enabled = true
        button_action = function() self:ShowContractDetails(contract) end

    elseif contract.type == "build" then
        button_text = "查看建筑"
        button_enabled = true
        button_action = function() self:ShowContractDetails(contract) end

    elseif contract.type == "collect" then
        button_text = "查看物品"
        button_enabled = true
        button_action = function() self:ShowContractDetails(contract) end

    elseif contract.type == "craft" then
        button_text = "查看配方"
        button_enabled = true
        button_action = function() self:ShowContractDetails(contract) end
    else
        return -- 未知类型，不创建按钮
    end

    -- 创建按钮
    local action_btn = card:AddChild(TEMPLATES.StandardButton(
        button_action,
        button_text,
        {100, 30}
    ))
    action_btn:SetPosition(0, -30)

    -- 内测：为每个合约增加“刷新此合约”按钮（不限）
    local refresh_btn = card:AddChild(TEMPLATES.StandardButton(
        function() self:DoRefreshSingleContract(index) end,
        "刷新此合约",
        {120, 30}
    ))
    refresh_btn:SetPosition(0, -66)
    table.insert(self.action_buttons, refresh_btn)

    -- 设置按钮状态和提示
    if not button_enabled then
        action_btn:SetTextColour(0.5, 0.5, 0.5, 1) -- 灰色表示不可用
        if action_btn.image then
            action_btn.image:SetTint(0.5, 0.5, 0.5, 1)
        end
    end

    -- 添加悬停提示
    local tooltip_text = self:GetContractButtonTooltip(contract, button_enabled)
    if tooltip_text and action_btn.SetHoverText then
        action_btn:SetHoverText(tooltip_text)
    end

    return action_btn
end

-- 获取合约按钮的提示文本
function CaravanScreen:GetContractButtonTooltip(contract, button_enabled)
    local target_name = contract.target_data.name or "未知"

    if contract.type == "deliver" then
        if button_enabled then
            return string.format("点击交付 %s\n需要：%d 个，进度：%d/%d",
                target_name, contract.goal - contract.progress, contract.progress, contract.goal)
        else
            local status_text = self:GetDeliveryStatusText(contract)
            return string.format("无法交付 %s\n%s", target_name, status_text)
        end
    elseif contract.type == "kill" then
        return string.format("查看击杀目标：%s\n需要击杀：%d 只，当前：%d/%d",
            target_name, contract.goal, contract.progress, contract.goal)
    elseif contract.type == "build" then
        return string.format("查看建造目标：%s\n需要建造：%d 个，当前：%d/%d",
            target_name, contract.goal, contract.progress, contract.goal)
    elseif contract.type == "collect" then
        return string.format("查看采集目标：%s\n需要采集：%d 个，当前：%d/%d",
            target_name, contract.goal, contract.progress, contract.goal)
    elseif contract.type == "craft" then
        return string.format("查看制作目标：%s\n需要制作：%d 个，当前：%d/%d",
            target_name, contract.goal, contract.progress, contract.goal)
    end

    return nil
end

-- 检查玩家是否能交付合约
function CaravanScreen:CanPlayerDeliverContract(contract)
    if not self.owner or not self.owner.components or not self.owner.components.inventory then
        return false
    end

    if contract.type ~= "deliver" or contract.completed then
        return false
    end

    local item_name = contract.target_data.item
    local needed = contract.goal - contract.progress
    local inventory = self.owner.components.inventory

    -- 计算玩家拥有的有效物品数量
    local has_count = 0
    local total_count = 0
    local invalid_reasons = {}

    for i = 1, inventory.maxslots do
        local item = inventory.itemslots[i]
        if item and item.prefab == item_name then
            local item_count = 1
            if item.components.stackable then
                item_count = item.components.stackable:StackSize()
            end
            total_count = total_count + item_count

            -- 详细的验证
            local valid = true
            local reason = ""

            -- 获取配置的验证要求
            local validation_config = self:GetDeliveryValidationConfig()

            -- 检查耐久度
            if item.components.finiteuses and validation_config and validation_config.min_durability_percent > 0 then
                local percent = item.components.finiteuses:GetPercent()
                if percent < validation_config.min_durability_percent then
                    valid = false
                    reason = string.format("耐久度不足(需要≥%d%%)", validation_config.min_durability_percent * 100)
                end
            end

            -- 检查新鲜度
            if item.components.perishable and validation_config and validation_config.min_freshness_percent > 0 then
                local percent = item.components.perishable:GetPercent()
                if percent < validation_config.min_freshness_percent then
                    valid = false
                    local freshness_req = string.format("新鲜度不足(需要≥%d%%)", validation_config.min_freshness_percent * 100)
                    reason = reason .. (reason ~= "" and "，" or "") .. freshness_req
                end
            end

            if valid then
                has_count = has_count + item_count
            else
                table.insert(invalid_reasons, string.format("%d个%s(%s)", item_count, contract.target_data.name, reason))
            end
        end
    end

    -- 存储检查结果用于显示
    self._delivery_check_result = {
        needed = needed,
        has_valid = has_count,
        has_total = total_count,
        invalid_reasons = invalid_reasons
    }

    return has_count >= needed
end

-- 获取交付状态信息
function CaravanScreen:GetDeliveryStatusText(contract)
    if not self._delivery_check_result then
        self:CanPlayerDeliverContract(contract)
    end

    local result = self._delivery_check_result
    if not result then return "无法检查物品" end

    local status_text = string.format("需要：%d，有效：%d，总计：%d",
        result.needed, result.has_valid, result.has_total)

    if #result.invalid_reasons > 0 then
        status_text = status_text .. "\n无效物品：" .. table.concat(result.invalid_reasons, "，")
    end

    -- 添加配置要求说明
    local validation_config = self:GetDeliveryValidationConfig()
    if validation_config then
        local requirements = {}
        if validation_config.min_durability_percent > 0 then
            table.insert(requirements, string.format("耐久≥%d%%", validation_config.min_durability_percent * 100))
        end
        if validation_config.min_freshness_percent > 0 then
            table.insert(requirements, string.format("新鲜≥%d%%", validation_config.min_freshness_percent * 100))
        end

        if #requirements > 0 then
            status_text = status_text .. "\n要求：" .. table.concat(requirements, "，")
        end
    end

    return status_text
end

-- 获取交付验证配置（UI侧仅使用TUNING）
function CaravanScreen:GetDeliveryValidationConfig()
    if TUNING and TUNING.CARAVAN then
        return {
            min_durability_percent = TUNING.CARAVAN.DELIVER_MIN_DURABILITY or 0.8,
            min_freshness_percent = TUNING.CARAVAN.DELIVER_MIN_FRESHNESS or 0.5
        }
    end
    return { min_durability_percent = 0.8, min_freshness_percent = 0.5 }
end

-- 执行交付合约（通过RPC）
function CaravanScreen:DoDeliverContract(contract_index, _)
    -- 播放点击音效
    local s = TheFrontEnd and TheFrontEnd:GetSound()
    if s and s.PlaySound then
        s:PlaySound("dontstarve/HUD/click_move")
    end

    -- 发送RPC到服务器
    SendModRPCToServer(GetModRPC("thinking","contract_deliver"), contract_index)

    -- UI会通过RPC回调自动更新，无需手动刷新
end

-- 显示合约详情

-- 内测：刷新单个合约（通过RPC）
function CaravanScreen:DoRefreshSingleContract(contract_index)
    -- 播放点击音效
    local s = TheFrontEnd and TheFrontEnd:GetSound()
    if s and s.PlaySound then
        s:PlaySound("dontstarve/HUD/click_move")
    end

    -- 发送RPC到服务器
    SendModRPCToServer(GetModRPC("thinking","contract_refresh_one"), contract_index)
    -- UI刷新将由服务器回传的合约快照触发（OnContractDataUpdated）
end

function CaravanScreen:ShowContractDetails(contract)
    if not contract or not self.owner then return end

    local details = ""
    local target_name = contract.target_data.name or "未知"

    if contract.type == "kill" then
        details = string.format("击杀目标：%s\n需要击杀：%d 只\n当前进度：%d/%d\n\n提示：击杀指定的生物来完成此合约",
            target_name, contract.goal, contract.progress, contract.goal)

    elseif contract.type == "deliver" then
        local status_text = self:GetDeliveryStatusText(contract)
        details = string.format("交付目标：%s\n需要交付：%d 个\n当前进度：%d/%d\n\n背包状态：\n%s\n\n提示：点击交付按钮来提交物品",
            target_name, contract.goal, contract.progress, contract.goal, status_text)

    elseif contract.type == "build" then
        details = string.format("建造目标：%s\n需要建造：%d 个\n当前进度：%d/%d\n\n提示：建造指定的建筑来完成此合约",
            target_name, contract.goal, contract.progress, contract.goal)

    elseif contract.type == "collect" then
        details = string.format("采集目标：%s\n需要采集：%d 个\n当前进度：%d/%d\n\n提示：采集指定的物品来完成此合约",
            target_name, contract.goal, contract.progress, contract.goal)

    elseif contract.type == "craft" then
        details = string.format("制作目标：%s\n需要制作：%d 个\n当前进度：%d/%d\n\n提示：制作指定的物品来完成此合约",
            target_name, contract.goal, contract.progress, contract.goal)
    end

    -- 添加奖励信息
    if contract.reward_favor then
        details = details .. string.format("\n奖励：%d 星尘", contract.reward_favor)
    end

    if contract.reward_rep then
        for faction, rep_amount in pairs(contract.reward_rep) do
            details = details .. string.format("，%s声望+%d", faction, rep_amount)
        end
    end

    -- 显示详情给玩家
    if self.owner.components and self.owner.components.talker then
        self.owner.components.talker:Say("合约详情：" .. target_name)
        -- 可以考虑添加一个详情弹窗，这里先用简单的聊天显示
        print("[合约详情]\n" .. details)
    end
end

-- 创建阵营声望专用UI
-- 献祭UI
function CaravanScreen:OnSacrificeDataUpdated(data)
    self.cached_sacrifice = data
    if self.current_tab == "sacrifice" and self.sacrifice_ui then
        self:UpdateSacrificeUIContent()
    end
end

function CaravanScreen:CreateSacrificeUI()
    -- 若无缓存则先请求
    if not self.cached_sacrifice then
        SendModRPCToServer(GetModRPC("thinking","sacrifice_request"))
        if self.info_text then self.info_text:Show(); self.info_text:SetString("献祭数据同步中...") end
        return
    end

    if not self.sacrifice_ui then
        self.sacrifice_ui = self.info_area:AddChild(Widget("sacrifice_ui"))
        self.sacrifice_ui:SetPosition(0, 30)

        self.sac_title = self.sacrifice_ui:AddChild(Text(CHATFONT, 32, "献祭轮盘"))
        SetColourSafe(self.sac_title, {1,1,0,1})
        self.sac_title:SetPosition(0, 200)

        self.sac_stats = self.sacrifice_ui:AddChild(Text(CHATFONT, 22, ""))
        SetColourSafe(self.sac_stats, {0.8,0.8,0.8,1})
        self.sac_stats:SetPosition(0, 160)

        self.sac_pending = self.sacrifice_ui:AddChild(Text(CHATFONT, 22, ""))
        SetColourSafe(self.sac_pending, {1,0.5,0.2,1})
        self.sac_pending:SetPosition(0, 130)

        -- 永久上限与诅咒、历史显示
        self.sac_caps = self.sacrifice_ui:AddChild(Text(CHATFONT, 22, ""))
        SetColourSafe(self.sac_caps, {0.7,1,0.7,1})
        self.sac_caps:SetPosition(0, 120)

        self.sac_caps_recent = self.sacrifice_ui:AddChild(Text(CHATFONT, 22, ""))
        SetColourSafe(self.sac_caps_recent, {0.6,0.9,0.6,1})
        self.sac_caps_recent:SetPosition(0, 95)

        self.sac_curses = self.sacrifice_ui:AddChild(Text(CHATFONT, 22, ""))
        SetColourSafe(self.sac_curses, {1,0.6,0.6,1})
        self.sac_curses:SetPosition(0, 70)

        self.sac_history_area = self.sacrifice_ui:AddChild(Widget("sac_history_area"))
        self.sac_history_area:SetPosition(0, 40)

        -- 创建5条历史文本控件
        self.sac_history_labels = {}
        for i=1,5 do
            local lbl = self.sac_history_area:AddChild(Text(CHATFONT, 20, ""))
            SetColourSafe(lbl, {1,1,1,1})
            lbl:SetPosition(0, 40 - (i-1)*18)
            table.insert(self.sac_history_labels, lbl)
        end

        -- 四个按钮 + 确认按钮
        local function mkbtn(x, txt, id, action)
            local b = self.sacrifice_ui:AddChild(TEMPLATES.StandardButton(function()
                -- 触发RPC
                if action == "confirm" then
                    if self.cached_sacrifice and self.cached_sacrifice.pending and self.cached_sacrifice.pending.token then
                        SendModRPCToServer(GetModRPC("thinking","sacrifice_action"), "confirm", tostring(self.cached_sacrifice.pending.t), tostring(self.cached_sacrifice.pending.token))
                    else
                        if self.owner and self.owner.components and self.owner.components.talker then
                            self.owner.components.talker:Say("暂无待确认令牌")
                        end
                    end
                else
                    SendModRPCToServer(GetModRPC("thinking","sacrifice_action"), "spin", id)
                end
            end, txt, {120,40}))
            b:SetPosition(x, -20)
            return b
        end
        self.btn_blood = mkbtn(-300, "血祭", "blood")
        self.btn_soul  = mkbtn(-140,  "魂祭", "soul")
        self.btn_hunger= mkbtn(20,   "饥祭", "hunger")
        self.btn_item  = mkbtn(180,  "物祭(手持)", "item")
        self.btn_confirm = mkbtn(340, "确认献祭", nil, "confirm")
    end

    self:UpdateSacrificeUIContent()

    -- 启动倒计时刷新任务（安全生命周期）
    if (not self._sac_timer_task) and self.owner and self.owner.DoPeriodicTask then
        self._sac_timer_task = self.owner:DoPeriodicTask(1, function()
            local screen = TheFrontEnd and TheFrontEnd:GetActiveScreen() or nil
            if not screen or screen ~= self or self.current_tab ~= "sacrifice" then
                if self._sac_timer_task then self._sac_timer_task:Cancel(); self._sac_timer_task = nil end
                return
            end
            self:UpdateSacrificeUIContent()
        end)
    end
end


function CaravanScreen:UpdateSacrificeUIContent()
    local d = self.cached_sacrifice or {}
    local daily_used = d.daily_used or 0
    local limit = (TUNING and TUNING.CARAVAN and TUNING.CARAVAN.SACRIFICE and TUNING.CARAVAN.SACRIFICE.LIMITS.DAILY_SPINS_LIMIT) or 3

    local stats_line = string.format("今日已献祭:%d/%d", daily_used, limit)
    if self.sac_stats then self.sac_stats:SetString(stats_line) end

    if d.pending and d.pending.token then
        local remain = math.max(0, math.floor((d.pending.time or 0) - (GetTime() or 0)))
        local txt = string.format("高风险献祭确认令牌: %s（%ds内有效）\n在聊天输入: spin confirm %s %s", d.pending.token, remain, tostring(d.pending.t), d.pending.token)
        self.sac_pending:Show()
        self.sac_pending:SetString(txt)
    else
        if self.sac_pending then self.sac_pending:Hide() end
    end

    -- 永久上限 + 诅咒 + 历史
    if d.permanent then
        local caps = string.format("累计上限变化：生命 %+d，理智 %+d，饥饿 %+d", d.permanent.health or 0, d.permanent.sanity or 0, d.permanent.hunger or 0)
        self.sac_caps:SetString(caps)
    end

    -- 本次变化（若历史第一条含有info）
    if d.history and d.history[1] and d.history[1].info then
        local inf = d.history[1].info
        local recent = string.format("本次上限变化：%s %+d", tostring(inf.cap_kind or "-"), inf.cap_delta or 0)
        self.sac_caps_recent:SetString(recent)
        self.sac_caps_recent:Show()
    else
        if self.sac_caps_recent then self.sac_caps_recent:SetString(""); self.sac_caps_recent:Hide() end
    end

    if d.curses then
        local nowday = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
        local parts = {}
        for _,c in ipairs(d.curses) do
            local remain = (c.expire_day or nowday) - nowday
            table.insert(parts, string.format("%s(%dd)", tostring(c.id), math.max(0, remain)))
        end
        self.sac_curses:SetString("诅咒："..table.concat(parts, "，"))
    end

    if d.history then
        local function tier_color(t)
            if t == "small" then return {0.8,0.8,0.8,1}, "·" end
            if t == "mid" then return {0.7,0.9,1.0,1}, "★" end
            if t == "big" then return {0.4,0.9,0.6,1}, "✦" end
            if t == "super" then return {1.0,0.8,0.3,1}, "✧" end
            if t == "legend" then return {1.0,0.6,0.2,1}, "◆" end
            if t == "myth" then return {1.0,0.3,0.3,1}, "◇" end
            return {1,1,1,1}, "?"
        end
        for i=1,5 do
            if self.sac_history_labels and self.sac_history_labels[i] then
                local lbl = self.sac_history_labels[i]
                if i <= #d.history then
                    local h = d.history[i]
                    local g = h.grade and ("-"..tostring(h.grade)) or ""
                    local color, icon = tier_color(h.result)
                    SetColourSafe(lbl, color)
                    lbl:SetString(string.format("%s [%s%s] %s", icon, tostring(h.type), g, tostring(h.result)))
                    lbl:Show()
                else
                    lbl:Hide()
                end
            end
        end
    end
end

-- 原声望UI
function CaravanScreen:CreateReputationUI()
    local player_rep = self.owner and self.owner.components and self.owner.components.modplayer_rep
    if not player_rep or not player_rep.reps then
        -- 如果没有数据，显示提示信息
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString("无法获取声望信息")
        end
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.reputation_ui then
        self:CreateReputationUIStructure()
    end

    -- 更新内容
    self:UpdateReputationUIContent(player_rep)
end

-- 创建阵营声望UI结构
function CaravanScreen:CreateReputationUIStructure()
    -- 创建声望UI容器
    self.reputation_ui = self.info_area:AddChild(Widget("reputation_ui"))
    self.reputation_ui:SetPosition(0, 30)

    -- 创建标题
    self.reputation_title = self.reputation_ui:AddChild(Text(CHATFONT, 32, "阵营声望"))
    SetColourSafe(self.reputation_title, {1, 1, 0, 1})
    self.reputation_title:SetPosition(0, 150)

    -- 创建说明文本
    self.reputation_desc = self.reputation_ui:AddChild(Text(CHATFONT, 20, "声望影响交易折扣、护航协助和隐藏商品"))
    SetColourSafe(self.reputation_desc, {0.8, 0.8, 0.8, 1})
    self.reputation_desc:SetPosition(0, 120)

    -- 创建声望容器
    self.reputation_container = self.reputation_ui:AddChild(Widget("reputation_container"))
    self.reputation_container:SetPosition(0, 60)

    -- 创建声望条存储
    self.reputation_bars = {}

    -- 创建效果说明
    self:CreateReputationEffects()
end

-- 创建声望效果说明
function CaravanScreen:CreateReputationEffects()
    local effects_y = -140

    -- 效果标题
    local effects_title = self.reputation_ui:AddChild(Text(CHATFONT, 26, "声望效果:"))
    SetColourSafe(effects_title, {1, 1, 1, 1})
    effects_title:SetPosition(0, effects_y)

    -- 效果列表
    local effects = {
        {text = "• 交易折扣", pos = {-120, effects_y - 30}},
        {text = "• 护航协助", pos = {0, effects_y - 30}},
        {text = "• 隐藏商品", pos = {120, effects_y - 30}}
    }

    for _, effect in ipairs(effects) do
        local effect_text = self.reputation_ui:AddChild(Text(CHATFONT, 22, effect.text))
        SetColourSafe(effect_text, {0.8, 0.8, 0.8, 1})
        effect_text:SetPosition(effect.pos[1], effect.pos[2])
    end
end

-- 更新声望UI内容
function CaravanScreen:UpdateReputationUIContent(player_rep)
    -- 清除旧的声望条
    if self.reputation_bars then
        for _, bar in pairs(self.reputation_bars) do
            bar:Kill()
        end
    end
    self.reputation_bars = {}

    -- 定义阵营信息
    local factions = {
        {name = "猪人阵营", key = "pig", color = {0.8, 0.6, 0.4, 1}},
        {name = "猫狸阵营", key = "cat", color = {0.4, 0.8, 0.8, 1}},
        {name = "兔人阵营", key = "bunny", color = {0.8, 0.4, 0.8, 1}}
    }

    -- 创建声望条
    for i, faction in ipairs(factions) do
        self:CreateReputationBar(i, faction, player_rep.reps[faction.key] or 0)
    end
end

-- 创建声望条
function CaravanScreen:CreateReputationBar(index, faction, reputation_value)
    local bar_y = 40 - (index - 1) * 60  -- 调整间距

    -- 创建声望条容器
    local bar_container = self.reputation_container:AddChild(Widget("reputation_bar_" .. faction.key))
    bar_container:SetPosition(0, bar_y)

    -- 创建阵营图标和名称
    local faction_name = bar_container:AddChild(Text(CHATFONT, 26, "● " .. faction.name))
    SetColourSafe(faction_name, faction.color)
    faction_name:SetPosition(-180, 0)

    -- 创建声望值和等级
    local max_reputation = GLOBAL.TUNING.CARAVAN.MAX_REPUTATION or 1000
    local level = math.floor(reputation_value / 100) + 1  -- 每100声望一级
    local reputation_text = bar_container:AddChild(Text(CHATFONT, 22, string.format("Lv.%d (%d)", level, reputation_value)))
    SetColourSafe(reputation_text, {1, 1, 1, 1})
    reputation_text:SetPosition(180, 0)

    -- 创建进度条背景
    local bar_bg = bar_container:AddChild(Image("images/global.xml", "square.tex"))
    bar_bg:SetScale(0.8, 0.2)
    bar_bg:SetTint(0.3, 0.3, 0.4, 0.6)  -- 更浅的背景色，更透明
    bar_bg:SetPosition(0, 0)

    -- 创建进度条填充（根据声望值计算）
    local fill_ratio = math.min(reputation_value / max_reputation, 1.0)

    if fill_ratio > 0 then
        local bar_fill = bar_container:AddChild(Image("images/global.xml", "square.tex"))
        bar_fill:SetScale(0.8 * fill_ratio, 0.2)
        bar_fill:SetTint(faction.color[1], faction.color[2], faction.color[3], 0.8)
        bar_fill:SetPosition(-0.4 * (1 - fill_ratio), 0) -- 左对齐
    end

    -- 存储声望条引用
    self.reputation_bars[faction.key] = bar_container
end

-- 创建商队信息专用UI
function CaravanScreen:CreateCaravanUI()
    -- 客户端使用缓存数据或显示同步中
    local caravan_data = nil

    -- 统一通过RPC快照
    caravan_data = self.cached_caravan
    if not caravan_data then
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString("商队信息同步中...")
        end
        SendModRPCToServer(GetModRPC("thinking","caravan_request"))
        return
    end

    if not caravan_data then
        if self.info_text then
            self.info_text:Show()
            self.info_text:SetString("无法获取商队信息")
        end
        return
    end

    -- 如果UI不存在，创建初始UI结构
    if not self.caravan_ui then
        self:CreateCaravanUIStructure()
    end

    -- 更新内容
    self:UpdateCaravanUIContent(caravan_data)
end

-- 创建商队信息UI结构
function CaravanScreen:CreateCaravanUIStructure()
    -- 创建商队UI容器
    self.caravan_ui = self.info_area:AddChild(Widget("caravan_ui"))
    self.caravan_ui:SetPosition(0, 30)

    -- 创建标题
    self.caravan_title = self.caravan_ui:AddChild(Text(CHATFONT, 32, "商队信息"))
    SetColourSafe(self.caravan_title, {1, 1, 0, 1})
    self.caravan_title:SetPosition(0, 200)

    -- 创建信息容器
    self.caravan_container = self.caravan_ui:AddChild(Widget("caravan_container"))
    self.caravan_container:SetPosition(0, 120)

    -- 创建功能说明
    self:CreateCaravanFunctions()
end

-- 创建商队功能说明
function CaravanScreen:CreateCaravanFunctions()
    local functions_y = -140

    -- 功能标题
    local functions_title = self.caravan_ui:AddChild(Text(CHATFONT, 26, "商队功能:"))
    SetColourSafe(functions_title, {1, 1, 1, 1})
    functions_title:SetPosition(0, functions_y)

    -- 功能列表
    local functions = {
        {text = "• 回收冗余资源", pos = {-140, functions_y - 30}},
        {text = "• 兑换星尘代币", pos = {0, functions_y - 30}},
        {text = "• 获取稀有蓝图", pos = {140, functions_y - 30}}
    }

    for _, func in ipairs(functions) do
        local func_text = self.caravan_ui:AddChild(Text(CHATFONT, 22, func.text))
        SetColourSafe(func_text, {0.8, 0.8, 0.8, 1})
        func_text:SetPosition(func.pos[1], func.pos[2])
    end
end

-- 更新商队UI内容
function CaravanScreen:UpdateCaravanUIContent(world_comp)
    -- 清除旧的信息
    if self.caravan_info then
        for _, info in pairs(self.caravan_info) do
            info:Kill()
        end
    end
    self.caravan_info = {}

    local next_day = world_comp.caravan.next_day or 0
    local current_day = (TheWorld and TheWorld.state and TheWorld.state.cycles) or 0
    local days_remaining = next_day - current_day

    -- 创建时间信息
    self:CreateCaravanTimeInfo(next_day, current_day, days_remaining)
end

-- 创建商队时间信息
function CaravanScreen:CreateCaravanTimeInfo(next_day, current_day, days_remaining)
    -- 下次商队时间
    local next_caravan = self.caravan_container:AddChild(Text(CHATFONT, 26, string.format("下次商队: 第 %d 天", next_day)))
    SetColourSafe(next_caravan, {0.8, 0.8, 0.2, 1})
    next_caravan:SetPosition(0, 60)
    table.insert(self.caravan_info, next_caravan)

    -- 当前天数
    local current_day_text = self.caravan_container:AddChild(Text(CHATFONT, 26, string.format("当前天数: 第 %d 天", current_day)))
    SetColourSafe(current_day_text, {0.8, 0.8, 0.8, 1})
    current_day_text:SetPosition(0, 20)
    table.insert(self.caravan_info, current_day_text)

    -- 状态信息
    local status_text = ""
    local status_color = {1, 1, 1, 1}

    if days_remaining <= 0 then
        status_text = "商队应该已经到达！"
        status_color = {0.2, 0.8, 0.2, 1} -- 绿色
    else
        status_text = string.format("还需等待 %d 天", days_remaining)
        status_color = {0.8, 0.8, 0.2, 1} -- 黄色
    end

    local status = self.caravan_container:AddChild(Text(CHATFONT, 28, status_text))
    SetColourSafe(status, status_color)
    status:SetPosition(0, -20)
    table.insert(self.caravan_info, status)

    -- 创建倒计时条（如果商队还没到）
    if days_remaining > 0 then
        self:CreateCountdownBar(days_remaining)
    end
end

-- 创建倒计时条
function CaravanScreen:CreateCountdownBar(days_remaining)
    local bar_y = -80

    -- 倒计时条背景
    local countdown_bg = self.caravan_container:AddChild(Image("images/global.xml", "square.tex"))
    countdown_bg:SetScale(1.0, 0.25)
    countdown_bg:SetTint(0.3, 0.3, 0.4, 0.6)  -- 更浅的背景色，更透明
    countdown_bg:SetPosition(0, bar_y)
    table.insert(self.caravan_info, countdown_bg)

    -- 倒计时条填充
    local max_wait_days = GLOBAL.TUNING.CARAVAN.MAX_CARAVAN_WAIT_DAYS
    local fill_ratio = math.max(0, 1 - (days_remaining / max_wait_days))

    if fill_ratio > 0 then
        local countdown_fill = self.caravan_container:AddChild(Image("images/global.xml", "square.tex"))
        countdown_fill:SetScale(1.0 * fill_ratio, 0.25)
        countdown_fill:SetTint(0.2, 0.8, 0.2, 0.8)
        countdown_fill:SetPosition(-0.5 * (1 - fill_ratio), bar_y) -- 左对齐
        table.insert(self.caravan_info, countdown_fill)
    end

    -- 倒计时文本
    local countdown_text = self.caravan_container:AddChild(Text(CHATFONT, 22, string.format("倒计时: %d 天", days_remaining)))
    SetColourSafe(countdown_text, {1, 1, 1, 1})
    countdown_text:SetPosition(0, bar_y)
    table.insert(self.caravan_info, countdown_text)
end

-- 创建被动恩惠UI结构（只在第一次调用）
function CaravanScreen:CreateBoonsUI(player_boons)
    -- 创建被动恩惠UI容器
    self.boons_ui = self.info_area:AddChild(Widget("boons_ui"))
    self.boons_ui:SetPosition(0, 30)

    -- 创建头部信息区域
    self.boons_header = self.boons_ui:AddChild(Text(CHATFONT, 30, ""))
    SetColourSafe(self.boons_header, {1, 1, 0, 1})
    self.boons_header:SetPosition(0, 200)

    -- 添加操作说明
    local help_text = "鼠标悬停查看详情，点击进行操作"
    local help = self.boons_ui:AddChild(Text(CHATFONT, 24, help_text))
    SetColourSafe(help, {0.8, 0.8, 0.8, 1})
    help:SetPosition(0, 160)

    -- 创建技能树布局
    self:CreateSkillTree(player_boons)

    -- 初始更新内容
    self:UpdateBoonsUIContent(player_boons)
end

-- 更新被动恩惠UI内容（增量更新）
function CaravanScreen:UpdateBoonsUIContent(player_boons)
    -- 更新头部信息
    if self.boons_header then
        local favor = player_boons.favor or 0
        local equipped_tbl = player_boons.equipped_boons or {}
        local max_equipped = player_boons.max_equipped or 2
        local header_text = string.format("星尘: %d    已装备: %d/%d",
            favor, #equipped_tbl, max_equipped)
        self.boons_header:SetString(header_text)
    end

    -- 更新所有按钮状态
    if self.boon_buttons then
        for boon_id, button_data in pairs(self.boon_buttons) do
            self:UpdateBoonButtonState(boon_id, button_data, player_boons)
        end
    end
end

-- 创建技能树布局（优化版）
function CaravanScreen:CreateSkillTree(player_boons)
    local available_boons = player_boons:GetAvailableBoons()

    -- 初始化按钮管理器
    self.boon_buttons = {}

    -- 定义职业分类和位置（调整位置让布局更合理）
    local categories = {
        {name = "斗士系", category = "warrior", pos = {-160, 60}, color = {1, 0.3, 0.3}},
        {name = "秘法系", category = "mage", pos = {160, 60}, color = {0.3, 0.3, 1}},
        {name = "巨兽系", category = "summoner", pos = {-160, -100}, color = {0.8, 0.3, 1}},
        {name = "农民系", category = "farmer", pos = {160, -100}, color = {0.3, 1, 0.3}}
    }

    for _, cat_info in ipairs(categories) do
        self:CreateCategorySection(cat_info, available_boons, player_boons)
    end
end



-- 创建职业分类区域
function CaravanScreen:CreateCategorySection(cat_info, available_boons, player_boons)
    -- 职业标题
    local title = self.boons_ui:AddChild(Text(CHATFONT, 28, cat_info.name))
    SetColourSafe(title, { (cat_info.color and cat_info.color[1]) or 1,
                           (cat_info.color and cat_info.color[2]) or 1,
                           (cat_info.color and cat_info.color[3]) or 1, 1 })
    title:SetPosition(cat_info.pos[1], cat_info.pos[2] + 60)

    -- 获取该职业的被动恩惠
    local category_boons = {}
    for boon_id, boon_def in pairs(available_boons) do
        if boon_def.category == cat_info.category then
            table.insert(category_boons, {id = boon_id, def = boon_def})
        end
    end

    -- 按费用排序
    table.sort(category_boons, function(a, b) return a.def.cost < b.def.cost end)

    -- 创建被动恩惠按钮
    for i, boon_info in ipairs(category_boons) do
        local y_offset = cat_info.pos[2] + 20 - (i - 1) * 45
        self:CreateBoonButton(boon_info.id, boon_info.def, cat_info.pos[1], y_offset, player_boons)
    end
end

-- 创建被动恩惠按钮（优化版）
function CaravanScreen:CreateBoonButton(boon_id, boon_def, x, y, player_boons)
    -- 创建按钮容器
    local button_container = self.boons_ui:AddChild(Widget("boon_button_" .. boon_id))
    button_container:SetPosition(x, y)

    -- 创建按钮
    local button = button_container:AddChild(TEMPLATES.StandardButton(
        function() self:OnBoonButtonClick(boon_id, boon_def, player_boons) end,
        boon_def.name,
        {180, 40}
    ))

    -- 添加Focus事件处理（饥荒UI标准，链式调用保留原有行为）
    local old_gain = button.OnGainFocus
    button.OnGainFocus = function(btn)
        if old_gain then old_gain(btn) end
        if btn.image then
            btn.image:SetTint(1, 1, 1, 1) -- 直接拉满
        end
    end

    local old_lose = button.OnLoseFocus
    button.OnLoseFocus = function(btn)
        if old_lose then old_lose(btn) end
        -- 恢复原始颜色（在UpdateBoonButtonState中设置）
        local button_data = self.boon_buttons[boon_id]
        if button_data then
            self:UpdateBoonButtonState(boon_id, button_data, player_boons)
        end
    end

    -- 存储按钮数据以便后续更新
    local button_data = {
        container = button_container,
        button = button,
        boon_def = boon_def
    }
    self.boon_buttons[boon_id] = button_data

    -- 初始化按钮状态
    self:UpdateBoonButtonState(boon_id, button_data, player_boons)

    return button_container
end

-- 更新单个按钮状态（增量更新）
function CaravanScreen:UpdateBoonButtonState(boon_id, button_data, player_boons)
    local boon_def = button_data.boon_def
    local button = button_data.button

    -- 安全的表引用，避免索引nil
    local unlocked_tbl = player_boons.unlocked_boons or {}
    local equipped_tbl = player_boons.equipped_boons or {}

    -- 检查状态
    local is_unlocked = unlocked_tbl[boon_id] and unlocked_tbl[boon_id] > 0
    local is_equipped = false
    for _, equipped_id in ipairs(equipped_tbl) do
        if equipped_id == boon_id then
            is_equipped = true
            break
        end
    end

    -- 确定按钮颜色和文本
    local button_color = {0.5, 0.5, 0.5, 1} -- 默认灰色
    local text_color = {1, 1, 1, 1}
    local button_text = boon_def.name
    local status_text = ""

    if is_equipped then
        button_color = {0.2, 0.8, 0.2, 1} -- 绿色（已装备）
        text_color = {1, 1, 1, 1}
        button_text = "✓ " .. boon_def.name
        status_text = "已装备"
    elseif is_unlocked then
        button_color = {0.8, 0.8, 0.2, 1} -- 黄色（已解锁）
        text_color = {0, 0, 0, 1}
        status_text = "已解锁"
    else
        button_color = {0.3, 0.3, 0.3, 1} -- 深灰色（未解锁）
        text_color = {0.7, 0.7, 0.7, 1}
        status_text = "未解锁"
    end

    -- 更新按钮外观
    if button.image then
        button.image:SetTint(button_color[1], button_color[2], button_color[3], button_color[4])
    end
    if button.text then
        button.text:SetColour(text_color[1], text_color[2], text_color[3], text_color[4])
        button:SetText(button_text)
    end

    -- 更新tooltip（添加desc安全检查）
    local desc = boon_def.desc or "暂无描述"
    local tooltip_text = string.format("%s\n\n%s\n\n费用: %d 星尘\n状态: %s",
        boon_def.name, desc, boon_def.cost, status_text)

    if is_equipped then
        tooltip_text = tooltip_text .. "\n\n点击卸下此被动技能"
    elseif is_unlocked then
        tooltip_text = tooltip_text .. "\n\n点击装备此被动技能"
        local max_equipped = player_boons.max_equipped or 2
        if #equipped_tbl >= max_equipped then
            tooltip_text = tooltip_text .. "\n注意: 装备槽已满"
        end
    else
        tooltip_text = tooltip_text .. "\n\n点击解锁此被动技能"
        local favor = player_boons.favor or 0
        if favor < boon_def.cost then
            tooltip_text = tooltip_text .. "\n注意: 星尘不足"
        end
    end

    button:SetHoverText(tooltip_text)
end

-- 处理被动恩惠按钮点击（优化版）
function CaravanScreen:OnBoonButtonClick(boon_id, boon_def, player_boons)
    -- 播放点击音效
    local s = TheFrontEnd and TheFrontEnd:GetSound()
    if s and s.PlaySound then s:PlaySound("dontstarve/HUD/click_move") end

    -- 客户端只发RPC，让服务端执行并回传最新快照
    local unlocked_tbl = player_boons.unlocked_boons or {}
    local equipped_tbl = player_boons.equipped_boons or {}

    local is_unlocked = unlocked_tbl[boon_id] and unlocked_tbl[boon_id] > 0
    local is_equipped = false
    for _, equipped_id in ipairs(equipped_tbl) do
        if equipped_id == boon_id then is_equipped = true break end
    end

    if is_equipped then
        SendModRPCToServer(GetModRPC("thinking","boons_action"), "unequip", boon_id)
    elseif is_unlocked then
        local max_equipped = player_boons.max_equipped or 2
        if #equipped_tbl >= max_equipped then
            if self.owner.components.talker then
                self.owner.components.talker:Say(string.format("✗ 装备槽已满 (%d/%d)，请先卸下其他被动技能", #equipped_tbl, max_equipped))
            end
            return
        end
        SendModRPCToServer(GetModRPC("thinking","boons_action"), "equip", boon_id)
    else
        local favor = player_boons.favor or 0
        if favor < (boon_def.cost or 0) then
            local needed = (boon_def.cost or 0) - favor
            if self.owner.components.talker then
                self.owner.components.talker:Say(string.format("✗ 星尘不足！需要 %d 星尘，还差 %d", boon_def.cost or 0, needed))
            end
            return
        end
        SendModRPCToServer(GetModRPC("thinking","boons_action"), "unlock", boon_id)
    end

    -- UI 刷新由服务器回传 boons_receive 触发
end

function CaravanScreen:RefreshData()
    self:UpdateContent()
end

-- 网络数据更新处理
function CaravanScreen:OnContractDataUpdated(contracts)
    if not contracts then return end

    self.cached_contracts = contracts

    -- 如果当前在合约标签页，刷新显示
    if self.current_tab == "contracts" then
        self:UpdateContractsUIContent({contracts = contracts})
    end
end

-- 获取合约数据（仅使用缓存的网络数据）
function CaravanScreen:GetContractData()
    -- 优先使用缓存的网络数据
    if self.cached_contracts and #self.cached_contracts > 0 then
        return {contracts = self.cached_contracts}
    end

    -- 统一改为RPC数据流，不直接读世界组件
    return nil
end

-- 检查是否正在同步数据
function CaravanScreen:IsSyncing()
    return not self.cached_contracts or #self.cached_contracts == 0
end

function CaravanScreen:Close()
    TheFrontEnd:PopScreen()
end

function CaravanScreen:OnControl(control, down)
    if CaravanScreen._base.OnControl(self, control, down) then
        return true
    end

    if not down and (control == CONTROL_CANCEL or control == CONTROL_MAP) then
        -- 播放关闭音效
        if TheFrontEnd and TheFrontEnd.GetSound and TheFrontEnd:GetSound() then
            local sound = TheFrontEnd:GetSound()
            if sound and sound.PlaySound then
                sound:PlaySound("dontstarve/HUD/click_move")
            end
        end
        self:Close()
        return true
    end

    return false
end

function CaravanScreen:GetHelpText()
    local controller_id = TheInput:GetControllerID()
    local t = {}
    table.insert(t, TheInput:GetLocalizedControl(controller_id, CONTROL_CANCEL) .. " " .. STRINGS.UI.HELP.BACK)
    return table.concat(t, "  ")
end

return CaravanScreen
