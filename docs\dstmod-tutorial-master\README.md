# Hello Modder

**欢迎那些有创意，但苦于不会写mod的玩家来参阅。本教程不适合<i style="color:red;">复制Modder</i>，请大家自觉维护工坊环境，不要成为复制Modder**

> 复制Modder指对别人开发的mod直接复制（包括但不限于：仅修改个名字，汉化（附属mod除外），制作合集）并重新上传发布的玩家

这个项目尽我所能总结出一些饥荒mod开发的教程，用于帮助想开发mod的玩家

**期待更多类似 `富贵险中求` `度日如年` `棱镜` `神话书说` `永不妥协` `勋章` 等等好玩的mod出现在工坊中**

------

**声明：除案例中的代码外其它所有页面里的代码都是代码片段，并不完整，不适用于复制粘贴！！！**

**分类说明**

- 入门：想学mod必看部分
- 进阶：当完全跟着入门操作一遍后，突然发现某些函数怎么拿来就用了，某个对象里都有哪些方法，某个方法里都有什么参数等这类疑问时，就可以看进阶部分整理的内容了，里面全是函数，方法等的定义用法等
- 高级：当看完进阶部分，api也都会用了，想写一些带有功能性的(component)，或者添加自定义的角色，生物等，就可以来看高级部分了，在高级里会介绍组件的定义，预制体的创建，脑子的写法等等
- 技巧：这部分总结一些概念性的东西，不是必看的内容，当然看了或许会让你对饥荒的游戏设计理解的更为透彻
- 案例：这里会选一些典型的例子来从头做一遍，保证只要跟着文章来操作，一定能成功的那种

**最后，我个人力量是有限的，诚挚邀请大佬们来一块参与维护这份中文饥荒教程文档，不管是PR形式的，还是私聊我跟我口诉的，来者不拒**
